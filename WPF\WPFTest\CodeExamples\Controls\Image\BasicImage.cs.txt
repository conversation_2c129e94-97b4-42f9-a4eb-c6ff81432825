// Image 基础用法 C# 代码示例
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.MediaControls
{
    public partial class ImagePageViewModel : ObservableObject
    {
        /// <summary>
        /// 当前选中的图片路径
        /// </summary>
        [ObservableProperty]
        public partial string SelectedImagePath { get; set; } = "pack://application:,,,/Assets/Images/sample.jpg";

        /// <summary>
        /// 当前拉伸模式
        /// </summary>
        [ObservableProperty]
        public partial Stretch CurrentStretch { get; set; } = Stretch.Uniform;

        /// <summary>
        /// 图片宽度
        /// </summary>
        [ObservableProperty]
        public partial double ImageWidth { get; set; } = 200;

        /// <summary>
        /// 图片高度
        /// </summary>
        [ObservableProperty]
        public partial double ImageHeight { get; set; } = 150;

        /// <summary>
        /// 是否显示边框
        /// </summary>
        [ObservableProperty]
        public partial bool ShowBorder { get; set; } = false;

        /// <summary>
        /// 边框厚度
        /// </summary>
        [ObservableProperty]
        public partial Thickness BorderThickness { get; set; } = new Thickness(2);

        /// <summary>
        /// 边框颜色
        /// </summary>
        [ObservableProperty]
        public partial Brush BorderBrush { get; set; } = Brushes.DodgerBlue;

        /// <summary>
        /// 选择图片命令
        /// </summary>
        [RelayCommand]
        private void SelectImage()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择图片文件",
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff|所有文件|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                SelectedImagePath = openFileDialog.FileName;
                StatusMessage = $"已选择图片: {System.IO.Path.GetFileName(openFileDialog.FileName)}";
            }
        }

        /// <summary>
        /// 设置拉伸模式命令
        /// </summary>
        [RelayCommand]
        private void SetStretchMode(string stretchMode)
        {
            CurrentStretch = stretchMode switch
            {
                "None" => Stretch.None,
                "Fill" => Stretch.Fill,
                "Uniform" => Stretch.Uniform,
                "UniformToFill" => Stretch.UniformToFill,
                _ => Stretch.Uniform
            };

            StatusMessage = $"拉伸模式已设置为: {CurrentStretch}";
        }

        /// <summary>
        /// 重置图片大小命令
        /// </summary>
        [RelayCommand]
        private void ResetImageSize()
        {
            ImageWidth = 200;
            ImageHeight = 150;
            StatusMessage = "图片大小已重置为默认值";
        }

        /// <summary>
        /// 切换边框显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleBorder()
        {
            ShowBorder = !ShowBorder;
            StatusMessage = ShowBorder ? "已显示边框" : "已隐藏边框";
        }
    }
}
