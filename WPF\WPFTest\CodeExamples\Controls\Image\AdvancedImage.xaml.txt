<!-- Image 高级用法示例 -->
<StackPanel Orientation="Vertical" Spacing="20">
    <!-- 图片画廊 -->
    <StackPanel>
        <TextBlock Text="图片画廊" FontWeight="Bold" Margin="0,0,0,12"/>
        <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
            <StackPanel Orientation="Horizontal" Spacing="12">
                <Border CornerRadius="8" Background="White" 
                        Effect="{StaticResource DropShadowEffect}">
                    <Image Source="pack://application:,,,/Assets/Images/gallery1.jpg"
                           Width="120" Height="90" Stretch="UniformToFill"/>
                </Border>
                <Border CornerRadius="8" Background="White"
                        Effect="{StaticResource DropShadowEffect}">
                    <Image Source="pack://application:,,,/Assets/Images/gallery2.jpg"
                           Width="120" Height="90" Stretch="UniformToFill"/>
                </Border>
                <Border CornerRadius="8" Background="White"
                        Effect="{StaticResource DropShadowEffect}">
                    <Image Source="pack://application:,,,/Assets/Images/gallery3.jpg"
                           Width="120" Height="90" Stretch="UniformToFill"/>
                </Border>
                <Border CornerRadius="8" Background="White"
                        Effect="{StaticResource DropShadowEffect}">
                    <Image Source="pack://application:,,,/Assets/Images/gallery4.jpg"
                           Width="120" Height="90" Stretch="UniformToFill"/>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </StackPanel>

    <!-- 响应式图片网格 -->
    <StackPanel>
        <TextBlock Text="响应式图片网格" FontWeight="Bold" Margin="0,0,0,12"/>
        <UniformGrid Columns="3" Rows="2" 
                     HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
            <Border Margin="4" CornerRadius="6" Background="LightGray">
                <Image Source="pack://application:,,,/Assets/Images/grid1.jpg"
                       Stretch="UniformToFill"/>
            </Border>
            <Border Margin="4" CornerRadius="6" Background="LightGray">
                <Image Source="pack://application:,,,/Assets/Images/grid2.jpg"
                       Stretch="UniformToFill"/>
            </Border>
            <Border Margin="4" CornerRadius="6" Background="LightGray">
                <Image Source="pack://application:,,,/Assets/Images/grid3.jpg"
                       Stretch="UniformToFill"/>
            </Border>
            <Border Margin="4" CornerRadius="6" Background="LightGray">
                <Image Source="pack://application:,,,/Assets/Images/grid4.jpg"
                       Stretch="UniformToFill"/>
            </Border>
            <Border Margin="4" CornerRadius="6" Background="LightGray">
                <Image Source="pack://application:,,,/Assets/Images/grid5.jpg"
                       Stretch="UniformToFill"/>
            </Border>
            <Border Margin="4" CornerRadius="6" Background="LightGray">
                <Image Source="pack://application:,,,/Assets/Images/grid6.jpg"
                       Stretch="UniformToFill"/>
            </Border>
        </UniformGrid>
    </StackPanel>

    <!-- 图片轮播 -->
    <StackPanel>
        <TextBlock Text="图片轮播" FontWeight="Bold" Margin="0,0,0,12"/>
        <Border CornerRadius="12" Background="White" 
                BorderBrush="LightGray" BorderThickness="1"
                Width="300" Height="200">
            <Grid>
                <!-- 图片容器 -->
                <Image x:Name="CarouselImage"
                       Source="{Binding CurrentCarouselImage}"
                       Stretch="UniformToFill"/>
                
                <!-- 控制按钮 -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Bottom"
                           Margin="0,0,0,16">
                    <Button Content="◀" Width="40" Height="40" 
                            Margin="4" CornerRadius="20"
                            Command="{Binding PreviousImageCommand}"/>
                    <Button Content="▶" Width="40" Height="40" 
                            Margin="4" CornerRadius="20"
                            Command="{Binding NextImageCommand}"/>
                </StackPanel>
                
                <!-- 指示器 -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Top"
                           Margin="0,16,0,0">
                    <Ellipse Width="8" Height="8" Fill="White" Margin="2" Opacity="0.5"/>
                    <Ellipse Width="8" Height="8" Fill="White" Margin="2" Opacity="1.0"/>
                    <Ellipse Width="8" Height="8" Fill="White" Margin="2" Opacity="0.5"/>
                    <Ellipse Width="8" Height="8" Fill="White" Margin="2" Opacity="0.5"/>
                </StackPanel>
            </Grid>
        </Border>
    </StackPanel>

    <!-- 图片缩放和平移 -->
    <StackPanel>
        <TextBlock Text="图片缩放和平移" FontWeight="Bold" Margin="0,0,0,12"/>
        <Border BorderBrush="Gray" BorderThickness="1" 
                Width="250" Height="180" CornerRadius="8">
            <ScrollViewer ZoomMode="Enabled" 
                         HorizontalScrollBarVisibility="Auto"
                         VerticalScrollBarVisibility="Auto">
                <Image Source="pack://application:,,,/Assets/Images/large-image.jpg"
                       Stretch="None"/>
            </ScrollViewer>
        </Border>
        <TextBlock Text="提示：使用鼠标滚轮缩放，拖拽平移" 
                   FontSize="12" Foreground="Gray" 
                   HorizontalAlignment="Center" Margin="0,8,0,0"/>
    </StackPanel>
</StackPanel>
