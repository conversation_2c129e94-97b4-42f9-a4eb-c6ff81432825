<!-- Image 特效和样式示例 -->
<StackPanel Orientation="Vertical" Spacing="20">
    <!-- 图片滤镜效果 -->
    <StackPanel>
        <TextBlock Text="图片滤镜效果" FontWeight="Bold" Margin="0,0,0,12"/>
        <WrapPanel>
            <!-- 原图 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="原图" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="120" Height="90" Stretch="UniformToFill"/>
            </StackPanel>

            <!-- 模糊效果 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="模糊效果" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="120" Height="90" Stretch="UniformToFill">
                    <Image.Effect>
                        <BlurEffect Radius="5"/>
                    </Image.Effect>
                </Image>
            </StackPanel>

            <!-- 阴影效果 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="阴影效果" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="120" Height="90" Stretch="UniformToFill">
                    <Image.Effect>
                        <DropShadowEffect Color="Black" Direction="315" 
                                         ShadowDepth="5" Opacity="0.5"/>
                    </Image.Effect>
                </Image>
            </StackPanel>

            <!-- 浮雕效果 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="浮雕效果" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="120" Height="90" Stretch="UniformToFill">
                    <Image.Effect>
                        <BevelBitmapEffect BevelWidth="5" EdgeProfile="BulgedUp"/>
                    </Image.Effect>
                </Image>
            </StackPanel>
        </WrapPanel>
    </StackPanel>

    <!-- 图片遮罩和裁剪 -->
    <StackPanel>
        <TextBlock Text="图片遮罩和裁剪" FontWeight="Bold" Margin="0,0,0,12"/>
        <WrapPanel>
            <!-- 圆形裁剪 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="圆形裁剪" FontSize="12" Margin="0,0,0,4"/>
                <Ellipse Width="100" Height="100">
                    <Ellipse.Fill>
                        <ImageBrush ImageSource="pack://application:,,,/Assets/Images/avatar.jpg"
                                   Stretch="UniformToFill"/>
                    </Ellipse.Fill>
                </Ellipse>
            </StackPanel>

            <!-- 星形裁剪 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="星形裁剪" FontSize="12" Margin="0,0,0,4"/>
                <Path Width="100" Height="100" 
                      Data="M 50,0 L 61,35 L 98,35 L 68,57 L 79,91 L 50,70 L 21,91 L 32,57 L 2,35 L 39,35 Z"
                      Stretch="Uniform">
                    <Path.Fill>
                        <ImageBrush ImageSource="pack://application:,,,/Assets/Images/sample.jpg"
                                   Stretch="UniformToFill"/>
                    </Path.Fill>
                </Path>
            </StackPanel>

            <!-- 心形裁剪 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="心形裁剪" FontSize="12" Margin="0,0,0,4"/>
                <Path Width="100" Height="100"
                      Data="M 50,90 C 50,90 10,50 10,30 C 10,10 30,10 50,30 C 70,10 90,10 90,30 C 90,50 50,90 50,90 Z"
                      Stretch="Uniform">
                    <Path.Fill>
                        <ImageBrush ImageSource="pack://application:,,,/Assets/Images/sample.jpg"
                                   Stretch="UniformToFill"/>
                    </Path.Fill>
                </Path>
            </StackPanel>
        </WrapPanel>
    </StackPanel>

    <!-- 图片动画效果 -->
    <StackPanel>
        <TextBlock Text="图片动画效果" FontWeight="Bold" Margin="0,0,0,12"/>
        <WrapPanel>
            <!-- 旋转动画 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="旋转动画" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="100" Height="100" Stretch="UniformToFill"
                       RenderTransformOrigin="0.5,0.5">
                    <Image.RenderTransform>
                        <RotateTransform x:Name="RotateTransform"/>
                    </Image.RenderTransform>
                    <Image.Triggers>
                        <EventTrigger RoutedEvent="Image.Loaded">
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever">
                                    <DoubleAnimation Storyboard.TargetName="RotateTransform"
                                                   Storyboard.TargetProperty="Angle"
                                                   From="0" To="360" Duration="0:0:4"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Image.Triggers>
                </Image>
            </StackPanel>

            <!-- 缩放动画 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="缩放动画" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="100" Height="100" Stretch="UniformToFill"
                       RenderTransformOrigin="0.5,0.5">
                    <Image.RenderTransform>
                        <ScaleTransform x:Name="ScaleTransform"/>
                    </Image.RenderTransform>
                    <Image.Triggers>
                        <EventTrigger RoutedEvent="Image.Loaded">
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever" AutoReverse="True">
                                    <DoubleAnimation Storyboard.TargetName="ScaleTransform"
                                                   Storyboard.TargetProperty="ScaleX"
                                                   From="1" To="1.2" Duration="0:0:1"/>
                                    <DoubleAnimation Storyboard.TargetName="ScaleTransform"
                                                   Storyboard.TargetProperty="ScaleY"
                                                   From="1" To="1.2" Duration="0:0:1"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Image.Triggers>
                </Image>
            </StackPanel>

            <!-- 透明度动画 -->
            <StackPanel Margin="0,0,16,16">
                <TextBlock Text="透明度动画" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                       Width="100" Height="100" Stretch="UniformToFill">
                    <Image.Triggers>
                        <EventTrigger RoutedEvent="Image.Loaded">
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever" AutoReverse="True">
                                    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                   From="1" To="0.3" Duration="0:0:2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Image.Triggers>
                </Image>
            </StackPanel>
        </WrapPanel>
    </StackPanel>

    <!-- 响应式图片 -->
    <StackPanel>
        <TextBlock Text="响应式图片" FontWeight="Bold" Margin="0,0,0,12"/>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 小屏幕 -->
            <StackPanel Grid.Column="0" Margin="4">
                <TextBlock Text="小屏幕 (Mobile)" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/mobile.jpg"
                       Stretch="UniformToFill" Height="120"/>
            </StackPanel>
            
            <!-- 中等屏幕 -->
            <StackPanel Grid.Column="1" Margin="4">
                <TextBlock Text="中等屏幕 (Tablet)" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/tablet.jpg"
                       Stretch="UniformToFill" Height="120"/>
            </StackPanel>
            
            <!-- 大屏幕 -->
            <StackPanel Grid.Column="2" Margin="4">
                <TextBlock Text="大屏幕 (Desktop)" FontSize="12" Margin="0,0,0,4"/>
                <Image Source="pack://application:,,,/Assets/Images/desktop.jpg"
                       Stretch="UniformToFill" Height="120"/>
            </StackPanel>
        </Grid>
    </StackPanel>
</StackPanel>
