using System.Windows.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Examples
{
    /// <summary>
    /// 基础 Badge 控件示例
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class BasicBadgeExample : UserControl
    {
        public BasicBadgeExample()
        {
            InitializeComponent();
            DataContext = new BasicBadgeViewModel();
        }
    }

    /// <summary>
    /// 基础 Badge 示例 ViewModel
    /// </summary>
    public partial class BasicBadgeViewModel : ObservableObject
    {
        #region Badge 属性

        /// <summary>
        /// Badge 内容
        /// </summary>
        [ObservableProperty]
        public partial string BadgeContent { get; set; } = "New";

        /// <summary>
        /// Badge 外观
        /// </summary>
        [ObservableProperty]
        public partial ControlAppearance BadgeAppearance { get; set; } = ControlAppearance.Primary;

        /// <summary>
        /// 是否显示 Badge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowBadge { get; set; } = true;

        /// <summary>
        /// 通知计数
        /// </summary>
        [ObservableProperty]
        public partial int NotificationCount { get; set; } = 5;

        /// <summary>
        /// 用户状态
        /// </summary>
        [ObservableProperty]
        public partial string UserStatus { get; set; } = "在线";

        /// <summary>
        /// 用户状态外观
        /// </summary>
        [ObservableProperty]
        public partial ControlAppearance UserStatusAppearance { get; set; } = ControlAppearance.Success;

        #endregion

        #region 构造函数

        public BasicBadgeViewModel()
        {
            // 初始化默认值
            InitializeDefaults();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 更改 Badge 内容命令
        /// </summary>
        [RelayCommand]
        private void ChangeBadgeContent()
        {
            var contents = new[] { "New", "Hot", "Sale", "VIP", "Pro", "Beta" };
            var random = new Random();
            BadgeContent = contents[random.Next(contents.Length)];
        }

        /// <summary>
        /// 更改 Badge 外观命令
        /// </summary>
        [RelayCommand]
        private void ChangeBadgeAppearance()
        {
            var appearances = new[]
            {
                ControlAppearance.Primary,
                ControlAppearance.Secondary,
                ControlAppearance.Success,
                ControlAppearance.Danger,
                ControlAppearance.Caution,
                ControlAppearance.Info
            };
            
            var random = new Random();
            BadgeAppearance = appearances[random.Next(appearances.Length)];
        }

        /// <summary>
        /// 切换 Badge 显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleBadgeVisibility()
        {
            ShowBadge = !ShowBadge;
        }

        /// <summary>
        /// 增加通知计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseNotificationCount()
        {
            NotificationCount++;
        }

        /// <summary>
        /// 减少通知计数命令
        /// </summary>
        [RelayCommand]
        private void DecreaseNotificationCount()
        {
            if (NotificationCount > 0)
            {
                NotificationCount--;
            }
        }

        /// <summary>
        /// 重置通知计数命令
        /// </summary>
        [RelayCommand]
        private void ResetNotificationCount()
        {
            NotificationCount = 0;
        }

        /// <summary>
        /// 更改用户状态命令
        /// </summary>
        [RelayCommand]
        private void ChangeUserStatus()
        {
            var statuses = new[]
            {
                ("在线", ControlAppearance.Success),
                ("忙碌", ControlAppearance.Warning),
                ("离开", ControlAppearance.Secondary),
                ("离线", ControlAppearance.Danger)
            };
            
            var random = new Random();
            var status = statuses[random.Next(statuses.Length)];
            UserStatus = status.Item1;
            UserStatusAppearance = status.Item2;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            // 可以在这里设置更多默认值
        }

        #endregion
    }
}
