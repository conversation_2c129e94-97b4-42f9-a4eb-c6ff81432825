namespace WPFTest.Views.NotificationControls;

/// <summary>
/// InfoBarPageView.xaml 的交互逻辑
/// 使用 Prism 的 ViewModelLocator 自动绑定 ViewModel
/// </summary>
public partial class InfoBarPageView
{
    /// <summary>
    /// 构造函数 - 使用 Prism ViewModelLocator 自动绑定
    /// ViewModel 将通过依赖注入容器自动创建和绑定
    /// </summary>
    public InfoBarPageView()
    {
        InitializeComponent();
        
        // 不需要手动设置 DataContext
        // Prism 的 ViewModelLocator 会自动处理：
        // 1. 根据命名约定找到对应的 ViewModel (InfoBarPageViewModel)
        // 2. 通过依赖注入容器创建 ViewModel 实例
        // 3. 自动设置 DataContext
    }
}
