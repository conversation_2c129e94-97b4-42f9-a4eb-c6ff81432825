<UserControl x:Class="WPFTest.Views.NotificationControls.InfoBarPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:viewmodels="clr-namespace:WPFTest.ViewModels.NotificationControls"
             xmlns:mvvm="http://prismlibrary.com/"
             d:DataContext="{d:DesignInstance Type=viewmodels:InfoBarPageViewModel}"
             mc:Ignorable="d"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="3000" d:DesignWidth="1000">

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="0,0,12,0">
        <StackPanel Margin="16">

            <!-- 页面标题 -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" 
                               Symbol="Info24" 
                               FontSize="32" 
                               Margin="0,0,16,0"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="InfoBar 信息栏控件"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    <TextBlock Text="WPF-UI 官方通知控件，支持多种信息类型和交互功能"
                               FontSize="14"
                               Margin="0,4,0,0"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               TextWrapping="Wrap"/>
                </StackPanel>
            </Grid>

            <!-- 基础功能展示 -->
            <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="展示 InfoBar 的基础功能和不同信息类型的显示效果"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- 示例展示区域 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="基础 InfoBar 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- InfoBar 示例区域 -->
                            <StackPanel Margin="12">
                                <!-- 信息类型 InfoBar -->
                                <ui:InfoBar Title="信息提示"
                                            Message="这是一个信息类型的 InfoBar，用于显示一般性信息。"
                                            Severity="Informational"
                                            IsOpen="{Binding IsInfoBarOpen}"
                                            IsClosable="True"
                                            Margin="0,0,0,12"/>

                                <!-- 成功类型 InfoBar -->
                                <ui:InfoBar Title="操作成功"
                                            Message="文件已成功保存到指定位置。"
                                            Severity="Success"
                                            IsOpen="{Binding IsSuccessBarOpen}"
                                            IsClosable="True"
                                            Margin="0,0,0,12"/>

                                <!-- 警告类型 InfoBar -->
                                <ui:InfoBar Title="警告提示"
                                            Message="磁盘空间不足，建议清理临时文件。"
                                            Severity="Warning"
                                            IsOpen="{Binding IsWarningBarOpen}"
                                            IsClosable="True"
                                            Margin="0,0,0,12"/>

                                <!-- 错误类型 InfoBar -->
                                <ui:InfoBar Title="错误信息"
                                            Message="网络连接失败，请检查网络设置后重试。"
                                            Severity="Error"
                                            IsOpen="{Binding IsErrorBarOpen}"
                                            IsClosable="True"
                                            Margin="0,0,0,0"/>
                            </StackPanel>
                            
                            <!-- 自动关闭设置 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,20,0,16">
                                <StackPanel>
                                    <TextBlock Text="⏰ 自动关闭设置：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <CheckBox Grid.Column="0"
                                                  Content="启用自动关闭"
                                                  IsChecked="{Binding EnableAutoClose}"
                                                  VerticalAlignment="Center"
                                                  Margin="0,0,16,0"/>

                                        <StackPanel Grid.Column="1"
                                                    Orientation="Horizontal"
                                                    VerticalAlignment="Center"
                                                    IsEnabled="{Binding EnableAutoClose}">
                                            <TextBlock Text="延迟时间："
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                            <ui:NumberBox Value="{Binding AutoCloseSeconds, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="1"
                                                          Maximum="60"
                                                          Width="120"
                                                          Margin="0,0,8,0"
                                                          ToolTip="设置自动关闭延迟时间（1-60秒）"/>
                                            <TextBlock Text="秒"
                                                       VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2"
                                                    Orientation="Horizontal"
                                                    VerticalAlignment="Center"
                                                    Margin="16,0,0,0">
                                            <TextBlock Text="{Binding AutoCloseCountdown}"
                                                       FontWeight="Medium"
                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                       Margin="0,0,8,0"/>
                                            <ui:Button Content="测试延迟"
                                                       Icon="{ui:SymbolIcon Timer24}"
                                                       Command="{Binding TestDelayCommand}"
                                                       Appearance="Secondary"
                                                       Padding="8,4"
                                                       FontSize="12"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- 控制按钮 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="控制操作：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    <WrapPanel>
                                        <ui:Button Content="显示信息栏"
                                                   Icon="{ui:SymbolIcon Info24}"
                                                   Command="{Binding ShowInfoBarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="显示成功栏"
                                                   Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                                   Command="{Binding ShowSuccessBarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="显示警告栏"
                                                   Icon="{ui:SymbolIcon Warning24}"
                                                   Command="{Binding ShowWarningBarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="显示错误栏"
                                                   Icon="{ui:SymbolIcon ErrorCircle24}"
                                                   Command="{Binding ShowErrorBarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="隐藏所有"
                                                   Icon="{ui:SymbolIcon EyeOff24}"
                                                   Command="{Binding HideAllBarsCommand}"
                                                   Appearance="Secondary"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="基础功能代码示例"
                        Language="XAML"
                        Description="展示 InfoBar 的基础用法和不同信息类型"
                        ShowTabs="True"
                        XamlCode="{Binding BasicXamlExample}"
                        CSharpCode="{Binding BasicCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

            <!-- 高级功能展示 -->
            <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="展示 InfoBar 的高级功能，包括自定义内容、动作按钮和动态控制"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- 高级示例展示区域 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="高级 InfoBar 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 带动作按钮的 InfoBar -->
                            <StackPanel Margin="0,0,0,12">
                                <ui:InfoBar Title="更新可用"
                                            Message="发现新版本 v2.1.0，包含重要安全更新和性能改进。"
                                            Severity="Informational"
                                            IsOpen="{Binding IsUpdateBarOpen}"
                                            IsClosable="True"/>
                                <ui:Button Content="立即更新"
                                           Icon="{ui:SymbolIcon ArrowDownload24}"
                                           Command="{Binding UpdateCommand}"
                                           Appearance="Primary"
                                           HorizontalAlignment="Right"
                                           Margin="0,8,0,0"
                                           Visibility="{Binding IsUpdateBarOpen, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                            
                            <!-- 带自定义内容的 InfoBar -->
                            <StackPanel Margin="0,0,0,12" Visibility="{Binding IsSyncBarOpen, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <ui:InfoBar Title="文件同步"
                                            Message="正在同步文件到云端，请稍候..."
                                            Severity="Warning"
                                            IsOpen="{Binding IsSyncBarOpen}"
                                            IsClosable="True"/>
                                <ui:Card Padding="16" Margin="0,8,0,0" Background="{DynamicResource LayerFillColorDefaultBrush}">
                                    <StackPanel>
                                        <ProgressBar Value="{Binding SyncProgress}"
                                                     Maximum="100"
                                                     Height="6"
                                                     Margin="0,0,0,8"/>
                                        <TextBlock Text="{Binding SyncProgressText}"
                                                   FontSize="12"
                                                   HorizontalAlignment="Center"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                    </StackPanel>
                                </ui:Card>
                            </StackPanel>
                            
                            <!-- 控制按钮 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,20,0,0">
                                <StackPanel>
                                    <TextBlock Text="高级控制：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    <WrapPanel>
                                        <ui:Button Content="显示更新提示"
                                                   Icon="{ui:SymbolIcon ArrowDownload24}"
                                                   Command="{Binding ShowUpdateBarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="开始同步"
                                                   Icon="{ui:SymbolIcon ArrowSync24}"
                                                   Command="{Binding StartSyncCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="停止同步"
                                                   Icon="{ui:SymbolIcon Pause24}"
                                                   Command="{Binding StopSyncCommand}"
                                                   Appearance="Secondary"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="高级功能代码示例"
                        Language="XAML"
                        Description="展示 InfoBar 的高级功能和自定义配置"
                        ShowTabs="True"
                        XamlCode="{Binding AdvancedXamlExample}"
                        CSharpCode="{Binding AdvancedCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

            <!-- CommunityToolkit.Mvvm 集成示例 -->
            <ui:CardExpander Header="🚀 CommunityToolkit.Mvvm 集成" IsExpanded="True" Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="展示如何使用 CommunityToolkit.Mvvm 的现代化 MVVM 模式来控制 InfoBar"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- MVVM 示例展示区域 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="MVVM 模式示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 状态显示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="当前状态：" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding CurrentStatus}" 
                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                                    <TextBlock Text="{Binding LastActionTime, StringFormat='最后操作时间: {0:HH:mm:ss}'}" 
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Margin="0,4,0,0"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- 动态 InfoBar -->
                            <ui:InfoBar Title="{Binding DynamicTitle}"
                                        Message="{Binding DynamicMessage}"
                                        Severity="{Binding DynamicSeverity}"
                                        IsOpen="{Binding IsDynamicBarOpen}"
                                        IsClosable="True"/>
                            
                            <!-- 操作按钮 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,20,0,0">
                                <StackPanel>
                                    <TextBlock Text="MVVM 操作：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    <WrapPanel>
                                        <ui:Button Content="模拟成功操作"
                                                   Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                                   Command="{Binding SimulateSuccessCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="模拟错误操作"
                                                   Icon="{ui:SymbolIcon ErrorCircle24}"
                                                   Command="{Binding SimulateErrorCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="模拟异步操作"
                                                   Icon="{ui:SymbolIcon ArrowClockwise24}"
                                                   Command="{Binding SimulateAsyncCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="清除状态"
                                                   Icon="{ui:SymbolIcon Delete24}"
                                                   Command="{Binding ClearStatusCommand}"
                                                   Appearance="Secondary"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="CommunityToolkit.Mvvm 集成示例"
                        Language="C#"
                        Description="展示如何使用现代化 MVVM 模式控制 InfoBar"
                        ShowTabs="True"
                        XamlCode="{Binding MvvmXamlExample}"
                        CSharpCode="{Binding MvvmCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>
</UserControl>
