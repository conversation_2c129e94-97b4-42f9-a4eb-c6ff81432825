using System;
using System.Collections.ObjectModel;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.WindowControls
{
    /// <summary>
    /// TitleBar 控件页面的 ViewModel，演示各种标题栏功能
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class TitleBarPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<TitleBarPageViewModel>();

        #region 基础属性 - 使用 Partial Properties 语法

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "欢迎使用 TitleBar 控件示例！";

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty]
        public partial DateTime LastUpdated { get; set; } = DateTime.Now;

        #endregion

        #region TitleBar 配置属性

        /// <summary>
        /// 标题栏标题
        /// </summary>
        [ObservableProperty]
        public partial string TitleBarTitle { get; set; } = "自定义标题栏";

        /// <summary>
        /// 是否显示最小化按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowMinimize { get; set; } = true;

        /// <summary>
        /// 是否显示最大化按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowMaximize { get; set; } = true;

        /// <summary>
        /// 是否显示关闭按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowClose { get; set; } = true;

        /// <summary>
        /// 是否可以最大化
        /// </summary>
        [ObservableProperty]
        public partial bool CanMaximize { get; set; } = true;

        /// <summary>
        /// 标题栏高度
        /// </summary>
        [ObservableProperty]
        public partial double TitleBarHeight { get; set; } = 35;

        /// <summary>
        /// 是否使用自定义图标
        /// </summary>
        [ObservableProperty]
        public partial bool UseCustomIcon { get; set; } = false;

        /// <summary>
        /// 当前选中的图标
        /// </summary>
        [ObservableProperty]
        public partial SymbolRegular SelectedIcon { get; set; } = SymbolRegular.Window24;

        #endregion

        #region 统计信息属性

        /// <summary>
        /// 按钮点击次数
        /// </summary>
        [ObservableProperty]
        public partial int ButtonClickCount { get; set; } = 0;

        /// <summary>
        /// 配置更改次数
        /// </summary>
        [ObservableProperty]
        public partial int ConfigChangeCount { get; set; } = 0;

        /// <summary>
        /// 窗口操作次数
        /// </summary>
        [ObservableProperty]
        public partial int WindowOperationCount { get; set; } = 0;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 TitleBar XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicTitleBarXaml { get; set; } = string.Empty;

        /// <summary>
        /// 基础 TitleBar C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicTitleBarCs { get; set; } = string.Empty;

        /// <summary>
        /// 高级 TitleBar XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedTitleBarXaml { get; set; } = string.Empty;

        /// <summary>
        /// 高级 TitleBar C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedTitleBarCs { get; set; } = string.Empty;

        #endregion

        #region 可用图标集合

        /// <summary>
        /// 可用的图标集合
        /// </summary>
        public ObservableCollection<IconItem> AvailableIcons { get; } = new();

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public TitleBarPageViewModel()
        {
            InitializeAvailableIcons();
            InitializeCodeExamples();
            _logger.Info("🪟 TitleBarPageViewModel 初始化完成");
        }

        #region 命令实现

        /// <summary>
        /// 应用配置命令
        /// </summary>
        [RelayCommand]
        private void ApplyConfiguration()
        {
            try
            {
                ConfigChangeCount++;
                LastUpdated = DateTime.Now;
                StatusMessage = $"✅ 配置已应用 - 标题: {TitleBarTitle}, 高度: {TitleBarHeight}px";
                _logger.Info($"应用TitleBar配置: {TitleBarTitle}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 应用配置失败: {ex.Message}";
                _logger.Error($"应用配置失败: {ex}");
            }
        }

        /// <summary>
        /// 重置配置命令
        /// </summary>
        [RelayCommand]
        private void ResetConfiguration()
        {
            try
            {
                TitleBarTitle = "自定义标题栏";
                ShowMinimize = true;
                ShowMaximize = true;
                ShowClose = true;
                CanMaximize = true;
                TitleBarHeight = 35;
                UseCustomIcon = false;
                SelectedIcon = SymbolRegular.Window24;

                ConfigChangeCount++;
                LastUpdated = DateTime.Now;
                StatusMessage = "🔄 配置已重置为默认值";
                _logger.Info("TitleBar配置已重置");
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 重置配置失败: {ex.Message}";
                _logger.Error($"重置配置失败: {ex}");
            }
        }

        /// <summary>
        /// 模拟最小化命令
        /// </summary>
        [RelayCommand]
        private void SimulateMinimize()
        {
            try
            {
                WindowOperationCount++;
                ButtonClickCount++;
                LastUpdated = DateTime.Now;
                StatusMessage = "📉 模拟最小化操作";
                _logger.Info("模拟最小化操作");
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 模拟最小化失败: {ex.Message}";
                _logger.Error($"模拟最小化失败: {ex}");
            }
        }

        /// <summary>
        /// 模拟最大化命令
        /// </summary>
        [RelayCommand]
        private void SimulateMaximize()
        {
            try
            {
                WindowOperationCount++;
                ButtonClickCount++;
                LastUpdated = DateTime.Now;
                StatusMessage = "📈 模拟最大化操作";
                _logger.Info("模拟最大化操作");
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 模拟最大化失败: {ex.Message}";
                _logger.Error($"模拟最大化失败: {ex}");
            }
        }

        /// <summary>
        /// 模拟关闭命令
        /// </summary>
        [RelayCommand]
        private void SimulateClose()
        {
            try
            {
                WindowOperationCount++;
                ButtonClickCount++;
                LastUpdated = DateTime.Now;
                StatusMessage = "❌ 模拟关闭操作";
                _logger.Info("模拟关闭操作");
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 模拟关闭失败: {ex.Message}";
                _logger.Error($"模拟关闭失败: {ex}");
            }
        }

        /// <summary>
        /// 显示统计信息命令
        /// </summary>
        [RelayCommand]
        private void ShowStatistics()
        {
            try
            {
                var stats = $"统计信息:\n" +
                           $"• 按钮点击: {ButtonClickCount} 次\n" +
                           $"• 配置更改: {ConfigChangeCount} 次\n" +
                           $"• 窗口操作: {WindowOperationCount} 次\n" +
                           $"• 最后更新: {LastUpdated:yyyy-MM-dd HH:mm:ss}";

                StatusMessage = "📊 统计信息已显示";
                _logger.Info($"TitleBar统计信息:\n{stats}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 显示统计失败: {ex.Message}";
                _logger.Error($"显示统计失败: {ex}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化可用图标
        /// </summary>
        private void InitializeAvailableIcons()
        {
            try
            {
                var icons = new[]
                {
                    new IconItem("窗口", SymbolRegular.Window24),
                    new IconItem("应用", SymbolRegular.Apps24),
                    new IconItem("设置", SymbolRegular.Settings24),
                    new IconItem("文档", SymbolRegular.Document24),
                    new IconItem("文件夹", SymbolRegular.Folder24),
                    new IconItem("图片", SymbolRegular.Image24),
                    new IconItem("音乐", SymbolRegular.MusicNote124),
                    new IconItem("视频", SymbolRegular.Video24),
                    new IconItem("工具", SymbolRegular.Wrench24),
                    new IconItem("信息", SymbolRegular.Info24)
                };

                AvailableIcons.Clear();
                foreach (var icon in icons)
                {
                    AvailableIcons.Add(icon);
                }

                _logger.Info($"初始化了 {AvailableIcons.Count} 个可用图标");
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化图标失败: {ex}");
            }
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                LoadCodeExamplesFromResources();
                SetDefaultCodeExamples();
                _logger.Info("代码示例初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化代码示例失败: {ex}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 从嵌入资源加载代码示例
        /// </summary>
        private void LoadCodeExamplesFromResources()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var basePath = "WPFTest.CodeExamples.WindowControls.TitleBar.";

            // 加载基础示例
            BasicTitleBarXaml = LoadEmbeddedResource(assembly, basePath + "BasicTitleBar.xaml.txt");
            BasicTitleBarCs = LoadEmbeddedResource(assembly, basePath + "BasicTitleBar.cs.txt");

            // 加载高级示例
            AdvancedTitleBarXaml = LoadEmbeddedResource(assembly, basePath + "AdvancedTitleBar.xaml.txt");
            AdvancedTitleBarCs = LoadEmbeddedResource(assembly, basePath + "AdvancedTitleBar.cs.txt");
        }

        /// <summary>
        /// 加载嵌入资源
        /// </summary>
        private string LoadEmbeddedResource(Assembly assembly, string resourceName)
        {
            try
            {
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream != null)
                {
                    using var reader = new System.IO.StreamReader(stream);
                    return reader.ReadToEnd();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"加载资源失败 {resourceName}: {ex.Message}");
            }
            return string.Empty;
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicTitleBarXaml = "<!-- 基础 TitleBar 示例 -->\n<ui:TitleBar Title=\"我的应用\" Height=\"35\" ShowMinimize=\"True\" ShowMaximize=\"True\" />";
            BasicTitleBarCs = "// 基础 TitleBar C# 示例\n[ObservableProperty]\npublic partial string TitleBarTitle { get; set; } = \"我的应用\";\n\n[ObservableProperty]\npublic partial bool ShowMinimize { get; set; } = true;";

            AdvancedTitleBarXaml = "<!-- 高级 TitleBar 示例 -->\n<ui:TitleBar Title=\"{Binding TitleBarTitle}\" Height=\"{Binding TitleBarHeight}\" ShowMinimize=\"{Binding ShowMinimize}\" ShowMaximize=\"{Binding ShowMaximize}\" CanMaximize=\"{Binding CanMaximize}\" />";
            AdvancedTitleBarCs = "// 高级 TitleBar C# 示例\npublic partial class TitleBarViewModel : ObservableObject\n{\n    [ObservableProperty]\n    public partial string TitleBarTitle { get; set; } = \"高级标题栏\";\n\n    [ObservableProperty]\n    public partial double TitleBarHeight { get; set; } = 35;\n}";
        }

        #endregion
    }

    /// <summary>
    /// 图标项模型
    /// </summary>
    public class IconItem
    {
        public string Name { get; set; }
        public SymbolRegular Symbol { get; set; }

        public IconItem(string name, SymbolRegular symbol)
        {
            Name = name;
            Symbol = symbol;
        }
    }
}
