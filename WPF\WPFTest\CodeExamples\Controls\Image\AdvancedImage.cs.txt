// Image 高级用法 C# 代码示例
using System;
using System.Collections.ObjectModel;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.MediaControls
{
    public partial class ImagePageViewModel : ObservableObject
    {
        private readonly DispatcherTimer _carouselTimer;
        private int _currentImageIndex = 0;

        /// <summary>
        /// 轮播图片集合
        /// </summary>
        public ObservableCollection<string> CarouselImages { get; } = new()
        {
            "pack://application:,,,/Assets/Images/carousel1.jpg",
            "pack://application:,,,/Assets/Images/carousel2.jpg",
            "pack://application:,,,/Assets/Images/carousel3.jpg",
            "pack://application:,,,/Assets/Images/carousel4.jpg"
        };

        /// <summary>
        /// 当前轮播图片
        /// </summary>
        [ObservableProperty]
        public partial string CurrentCarouselImage { get; set; } = string.Empty;

        /// <summary>
        /// 图片画廊集合
        /// </summary>
        public ObservableCollection<string> GalleryImages { get; } = new()
        {
            "pack://application:,,,/Assets/Images/gallery1.jpg",
            "pack://application:,,,/Assets/Images/gallery2.jpg",
            "pack://application:,,,/Assets/Images/gallery3.jpg",
            "pack://application:,,,/Assets/Images/gallery4.jpg",
            "pack://application:,,,/Assets/Images/gallery5.jpg",
            "pack://application:,,,/Assets/Images/gallery6.jpg"
        };

        /// <summary>
        /// 是否自动播放轮播
        /// </summary>
        [ObservableProperty]
        public partial bool IsAutoPlay { get; set; } = true;

        /// <summary>
        /// 轮播间隔（秒）
        /// </summary>
        [ObservableProperty]
        public partial int CarouselInterval { get; set; } = 3;

        /// <summary>
        /// 当前图片索引
        /// </summary>
        [ObservableProperty]
        public partial int CurrentImageIndex { get; set; } = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImagePageViewModel()
        {
            // 初始化轮播定时器
            _carouselTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(CarouselInterval)
            };
            _carouselTimer.Tick += CarouselTimer_Tick;

            // 设置初始图片
            if (CarouselImages.Count > 0)
            {
                CurrentCarouselImage = CarouselImages[0];
            }

            // 开始自动播放
            if (IsAutoPlay)
            {
                _carouselTimer.Start();
            }
        }

        /// <summary>
        /// 轮播定时器事件
        /// </summary>
        private void CarouselTimer_Tick(object? sender, EventArgs e)
        {
            NextImage();
        }

        /// <summary>
        /// 下一张图片命令
        /// </summary>
        [RelayCommand]
        private void NextImage()
        {
            if (CarouselImages.Count == 0) return;

            _currentImageIndex = (_currentImageIndex + 1) % CarouselImages.Count;
            CurrentCarouselImage = CarouselImages[_currentImageIndex];
            CurrentImageIndex = _currentImageIndex;
            
            StatusMessage = $"切换到第 {_currentImageIndex + 1} 张图片";
        }

        /// <summary>
        /// 上一张图片命令
        /// </summary>
        [RelayCommand]
        private void PreviousImage()
        {
            if (CarouselImages.Count == 0) return;

            _currentImageIndex = _currentImageIndex == 0 
                ? CarouselImages.Count - 1 
                : _currentImageIndex - 1;
            
            CurrentCarouselImage = CarouselImages[_currentImageIndex];
            CurrentImageIndex = _currentImageIndex;
            
            StatusMessage = $"切换到第 {_currentImageIndex + 1} 张图片";
        }

        /// <summary>
        /// 切换自动播放命令
        /// </summary>
        [RelayCommand]
        private void ToggleAutoPlay()
        {
            IsAutoPlay = !IsAutoPlay;
            
            if (IsAutoPlay)
            {
                _carouselTimer.Start();
                StatusMessage = "已开启自动播放";
            }
            else
            {
                _carouselTimer.Stop();
                StatusMessage = "已关闭自动播放";
            }
        }

        /// <summary>
        /// 设置轮播间隔命令
        /// </summary>
        [RelayCommand]
        private void SetCarouselInterval(string interval)
        {
            if (int.TryParse(interval, out int seconds) && seconds > 0)
            {
                CarouselInterval = seconds;
                _carouselTimer.Interval = TimeSpan.FromSeconds(seconds);
                StatusMessage = $"轮播间隔已设置为 {seconds} 秒";
            }
        }

        /// <summary>
        /// 跳转到指定图片命令
        /// </summary>
        [RelayCommand]
        private void GoToImage(string indexString)
        {
            if (int.TryParse(indexString, out int index) && 
                index >= 0 && index < CarouselImages.Count)
            {
                _currentImageIndex = index;
                CurrentCarouselImage = CarouselImages[index];
                CurrentImageIndex = index;
                StatusMessage = $"已跳转到第 {index + 1} 张图片";
            }
        }

        /// <summary>
        /// 添加图片到画廊命令
        /// </summary>
        [RelayCommand]
        private void AddToGallery()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择要添加的图片",
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff|所有文件|*.*",
                FilterIndex = 1,
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                foreach (var fileName in openFileDialog.FileNames)
                {
                    GalleryImages.Add(fileName);
                }
                
                StatusMessage = $"已添加 {openFileDialog.FileNames.Length} 张图片到画廊";
            }
        }

        /// <summary>
        /// 清空画廊命令
        /// </summary>
        [RelayCommand]
        private void ClearGallery()
        {
            GalleryImages.Clear();
            StatusMessage = "画廊已清空";
        }
    }
}
