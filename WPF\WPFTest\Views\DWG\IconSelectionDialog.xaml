<Window x:Class="WPFTest.Views.DWG.IconSelectionDialog"
        x:Name="IconDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:local="clr-namespace:WPFTest.Views.DWG"
        Title="选择图标"
        Width="600"
        Height="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">



    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 当前选中的图标显示 -->
        <Border Grid.Row="0" 
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                BorderThickness="1" 
                CornerRadius="4" 
                Padding="16"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                Margin="0,0,0,16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="{Binding SelectedIcon}" 
                           FontSize="32" 
                           VerticalAlignment="Center"
                           Margin="0,0,16,0"/>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="当前选中的图标" FontWeight="SemiBold"/>
                    <TextBlock Text="{Binding SelectedIcon, StringFormat='字符: {0}'}" 
                               FontSize="12"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 分类选择 -->
        <ComboBox Grid.Row="1"
                  ItemsSource="{Binding IconCategories}"
                  SelectedItem="{Binding SelectedIconCategory}"
                  Margin="0,0,0,16"
                  HorizontalAlignment="Left"
                  MinWidth="150">
            <ComboBox.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding}"/>
                </DataTemplate>
            </ComboBox.ItemTemplate>
        </ComboBox>

        <!-- 图标网格 -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="0,0,0,16">
            <ItemsControl ItemsSource="{Binding FilteredIcons}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="8"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Button Click="OnIconClick"
                                Tag="{Binding Icon}"
                                ToolTip="{Binding Name}"
                                Margin="2"
                                Padding="8"
                                Background="Transparent"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                                        </Trigger>
                                        <!-- 选中状态将通过代码处理 -->
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <StackPanel>
                                <TextBlock Text="{Binding Icon}" 
                                           FontSize="24" 
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,4"/>
                                <TextBlock Text="{Binding Name}" 
                                           FontSize="10" 
                                           HorizontalAlignment="Center"
                                           TextWrapping="Wrap"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </Button>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 按钮 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <ui:Button Content="确定"
                       Click="OnOkClick"
                       Appearance="Primary"
                       Margin="0,0,8,0"
                       Padding="16,8"/>
            <ui:Button Content="取消"
                       Click="OnCancelClick"
                       Padding="16,8"/>
        </StackPanel>
    </Grid>
</Window>
