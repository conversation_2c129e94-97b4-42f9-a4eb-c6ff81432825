using System.Windows.Controls;
using WPFTest.ViewModels.MediaControls;

namespace WPFTest.Views.MediaControls
{
    /// <summary>
    /// BadgePageView.xaml 的交互逻辑
    /// </summary>
    public partial class BadgePageView : UserControl
    {
        /// <summary>
        /// 初始化 BadgePageView 实例
        /// </summary>
        public BadgePageView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化 BadgePageView 实例并设置 ViewModel
        /// </summary>
        /// <param name="viewModel">Badge 页面 ViewModel</param>
        public BadgePageView(BadgePageViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
