FileExplorerControl<UserControl
    Focusable="True"
    KeyDown="UserControl_KeyDown"
    d:DesignHeight="600"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Zylo.WPF.Controls.YFile.FileExplorerControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:Zylo.WPF.Controls.YFile.Model"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  添加资源定义  -->
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

        <!--  DataGrid文本列样式  -->
        <Style TargetType="TextBlock" x:Key="DataGridTextColumnStyle">
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="8,4" />
            <Setter Property="TextTrimming" Value="CharacterEllipsis" />
        </Style>

        <!--  DataGrid行样式 - 添加鼠标悬停效果  -->
        <Style TargetType="DataGridRow" x:Key="DataGridRowStyle">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="MinHeight" Value="32" />
            <Setter Property="Margin" Value="0,1" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08" />
                        </Setter.Value>
                    </Setter>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15" />
                        </Setter.Value>
                    </Setter>
                    <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}" />
                    <Setter Property="BorderThickness" Value="2,0,0,0" />
                </Trigger>
                <MultiTrigger>
                    <MultiTrigger.Conditions>
                        <Condition Property="IsSelected" Value="True" />
                        <Condition Property="IsMouseOver" Value="True" />
                    </MultiTrigger.Conditions>
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.2" />
                        </Setter.Value>
                    </Setter>
                </MultiTrigger>
            </Style.Triggers>
        </Style>

        <!--  ListView 整体样式  -->
        <Style TargetType="ListView" x:Key="FileExplorerListViewStyle">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListView">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer Background="Transparent" x:Name="PART_ScrollViewer">
                                <ScrollViewer.Resources>
                                    <!--  移除 GridViewHeaderRowPresenter 的默认背景  -->
                                    <Style TargetType="GridViewHeaderRowPresenter">
                                        <Setter Property="Height" Value="Auto" />
                                    </Style>
                                </ScrollViewer.Resources>
                                <ItemsPresenter />
                            </ScrollViewer>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>



        <!--  GridView 列标题统一样式  -->
        <Style TargetType="GridViewColumnHeader" x:Key="FileExplorerGridViewColumnHeaderStyle">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="0,0,0,1" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="HorizontalContentAlignment" Value="Left" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="FontSize" Value="13" />
            <Setter Property="MinHeight" Value="36" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08" />
                        </Setter.Value>
                    </Setter>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background">
                        <Setter.Value>
                            <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15" />
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  顶部工具栏 - 现代化布局  -->
        <Border
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="0"
            Padding="12,8"
            Visibility="{Binding ShowToolbar, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  左侧：标题和导航按钮  -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <ui:SymbolIcon
                        FontSize="18"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Margin="0,0,8,0"
                        Symbol="Folder24" />
                    <TextBlock
                        FontSize="14"
                        FontWeight="SemiBold"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Text="文件浏览器"
                        VerticalAlignment="Center"
                        Margin="0,0,16,0" />

                    <!--  导航按钮组  -->
                    <ui:Button
                        Appearance="Secondary"
                        Click="GoToRoot_Click"
                        Margin="0,0,4,0"
                        Padding="8,6"
                        ToolTip="主页">
                        <ui:Button.Icon>
                            <ui:SymbolIcon FontSize="16" Symbol="Home24" />
                        </ui:Button.Icon>
                    </ui:Button>
                    <ui:Button
                        Appearance="Secondary"
                        Click="GoToParent_Click"
                        Margin="0,0,4,0"
                        Padding="8,6"
                        ToolTip="上级目录">
                        <ui:Button.Icon>
                            <ui:SymbolIcon FontSize="16" Symbol="ArrowUp24" />
                        </ui:Button.Icon>
                    </ui:Button>
                    <ui:Button
                        Appearance="Secondary"
                        Click="Refresh_Click"
                        Margin="0,0,8,0"
                        Padding="8,6"
                        ToolTip="刷新">
                        <ui:Button.Icon>
                            <ui:SymbolIcon FontSize="16" Symbol="ArrowClockwise24" />
                        </ui:Button.Icon>
                    </ui:Button>
                </StackPanel>

                <!--  中间：搜索框  -->
                <ui:TextBox
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    MaxWidth="400"
                    MinWidth="250"
                    PlaceholderText="搜索文件和文件夹..."
                    Text="{Binding SearchText, RelativeSource={RelativeSource AncestorType=UserControl}, UpdateSourceTrigger=PropertyChanged}"
                    Visibility="{Binding ShowSearchBox, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"
                    x:Name="SearchTextBox">
                    <ui:TextBox.Icon>
                        <ui:SymbolIcon Symbol="Search24" />
                    </ui:TextBox.Icon>
                </ui:TextBox>

                <!--  右侧：视图和设置按钮  -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <ui:Button
                        Appearance="Secondary"
                        Padding="8,6"
                        ToolTip="列表视图">
                        <ui:Button.Icon>
                            <ui:SymbolIcon FontSize="16" Symbol="List24" />
                        </ui:Button.Icon>
                    </ui:Button>
                    <ui:Button
                        Appearance="Secondary"
                        Margin="4,0,0,0"
                        Padding="8,6"
                        ToolTip="更多选项">
                        <ui:Button.Icon>
                            <ui:SymbolIcon FontSize="16" Symbol="MoreHorizontal24" />
                        </ui:Button.Icon>
                    </ui:Button>
                </StackPanel>
            </Grid>
        </Border>

        <!--  面包屑导航栏 - 独立行  -->
        <Border
            Background="{DynamicResource ControlFillColorSecondaryBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="1"
            Padding="12,6"
            Visibility="{Binding ShowBreadcrumb, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
            <ScrollViewer
                HorizontalScrollBarVisibility="Auto"
                VerticalScrollBarVisibility="Disabled">
                <ui:BreadcrumbBar
                    ItemClicked="BreadcrumbBar_ItemClicked"
                    ItemsSource="{Binding BreadcrumbItems, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    x:Name="PathBreadcrumbBar">
                    <ui:BreadcrumbBar.ItemTemplate>
                        <DataTemplate DataType="{x:Type models:BreadcrumbItem}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    FontSize="13"
                                    Margin="0,0,4,0"
                                    Text="{Binding Icon}"
                                    VerticalAlignment="Center" />
                                <TextBlock
                                    FontSize="13"
                                    Text="{Binding Name}"
                                    VerticalAlignment="Center" />
                            </StackPanel>
                        </DataTemplate>
                    </ui:BreadcrumbBar.ItemTemplate>
                </ui:BreadcrumbBar>
            </ScrollViewer>
        </Border>

        <!--  地址栏 - 简洁设计  -->
        <Border
            Background="{DynamicResource ControlFillColorTertiaryBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="2"
            Padding="12,6"
            Visibility="{Binding ShowAddressBar, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Grid.Column="0"
                    Margin="0,0,8,0"
                    Text="路径:"
                    VerticalAlignment="Center"
                    FontSize="12" />

                <ui:TextBox
                    Grid.Column="1"
                    KeyDown="AddressBar_KeyDown"
                    Margin="0,0,8,0"
                    PlaceholderText="请输入或选择文件夹路径"
                    Text="{Binding CurrentPath, UpdateSourceTrigger=PropertyChanged}"
                    FontSize="12" />

                <ui:Button
                    Appearance="Secondary"
                    Click="Browse_Click"
                    Content="浏览"
                    Grid.Column="2"
                    Padding="8,4"
                    FontSize="12">
                    <ui:Button.Icon>
                        <ui:SymbolIcon FontSize="14" Symbol="FolderOpen24" />
                    </ui:Button.Icon>
                </ui:Button>
            </Grid>
        </Border>

        <!--  主内容区域  -->
        <Grid Grid.Row="3" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  左侧文件夹树  -->
            <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Grid.Column="0"
                Visibility="{Binding ShowFolderTree, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"
                Width="{Binding FolderTreeWidth, RelativeSource={RelativeSource AncestorType=UserControl}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <ui:Button
                        Appearance="Transparent"
                        Click="FolderTitle_Click"
                        Grid.Row="0"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Left"
                        Margin="8,8,8,4"
                        Padding="8,6">
                        <ui:Button.Icon>
                            <ui:SymbolIcon Foreground="{DynamicResource TextFillColorPrimaryBrush}" Symbol="Folder24" />
                        </ui:Button.Icon>
                        <ui:Button.Content>
                            <TextBlock
                                FontWeight="SemiBold"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Text="文件夹" />
                        </ui:Button.Content>
                    </ui:Button>

                    <TreeView
                        Background="Transparent"
                        BorderThickness="0"
                        Grid.Row="1"
                        Margin="8,4,8,8"
                        SelectedItemChanged="FolderTreeView_SelectedItemChanged"
                        x:Name="FolderTreeView">
                        <TreeView.ItemContainerStyle>
                            <Style BasedOn="{StaticResource {x:Type TreeViewItem}}" TargetType="TreeViewItem">
                                <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}" />
                                <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}" />
                                <Setter Property="Background" Value="Transparent" />
                                <Setter Property="BorderThickness" Value="0" />
                                <Setter Property="Padding" Value="6,4" />
                                <Setter Property="Margin" Value="0,1" />
                                <Setter Property="MinHeight" Value="32" />
                                <EventSetter Event="Expanded" Handler="TreeViewItem_Expanded" />
                                <EventSetter Event="Collapsed" Handler="TreeViewItem_Collapsed" />
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08" />
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15" />
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}" />
                                        <Setter Property="BorderThickness" Value="2,0,0,0" />
                                    </Trigger>
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True" />
                                            <Condition Property="IsMouseOver" Value="True" />
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.2" />
                                            </Setter.Value>
                                        </Setter>
                                    </MultiTrigger>
                                </Style.Triggers>
                            </Style>
                        </TreeView.ItemContainerStyle>
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate DataType="{x:Type models:FolderTreeItem}" ItemsSource="{Binding Children}">
                                <Border
                                    Background="Transparent"
                                    CornerRadius="4"
                                    Margin="0"
                                    Padding="4,2">
                                    <StackPanel Orientation="Horizontal">
                                        <!--  图标显示  -->
                                        <TextBlock
                                            FontSize="16"
                                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                            Margin="0,0,8,0"
                                            Text="{Binding Icon}"
                                            VerticalAlignment="Center" />

                                        <!--  文件夹名称  -->
                                        <TextBlock
                                            FontSize="13"
                                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                            Text="{Binding Name}"
                                            VerticalAlignment="Center" />

                                        <!--  虚拟节点指示器  -->
                                        <TextBlock
                                            FontSize="12"
                                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                            Margin="4,0,0,0"
                                            Opacity="0.7"
                                            Text="⏳"
                                            VerticalAlignment="Center"
                                            Visibility="{Binding IsVirtual, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </Border>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </Grid>
            </Border>

            <!--  分隔线  -->
            <GridSplitter
                Background="{DynamicResource ControlStrokeColorDefaultBrush}"
                Grid.Column="1"
                HorizontalAlignment="Stretch"
                Visibility="{Binding ShowFolderTree, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"
                Width="1" />

            <!--  右侧文件列表  -->
            <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Grid.Column="2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <ui:Button
                        Appearance="Transparent"
                        Click="FileTitle_Click"
                        Grid.Row="0"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Left"
                        Margin="8,8,8,4"
                        Padding="8,6">
                        <ui:Button.Icon>
                            <ui:SymbolIcon Foreground="{DynamicResource TextFillColorPrimaryBrush}" Symbol="Document24" />
                        </ui:Button.Icon>
                        <ui:Button.Content>
                            <TextBlock
                                FontWeight="SemiBold"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Text="文件" />
                        </ui:Button.Content>
                    </ui:Button>

                    <DataGrid
                        AutoGenerateColumns="False"
                        Background="Transparent"
                        BorderThickness="0"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        CanUserReorderColumns="True"
                        CanUserResizeColumns="True"
                        CanUserResizeRows="False"
                        CanUserSortColumns="True"
                        Grid.Row="1"
                        GridLinesVisibility="None"
                        HeadersVisibility="Column"
                        IsReadOnly="True"
                        KeyDown="FileListView_KeyDown"
                        Margin="8,4,8,8"
                        MouseDoubleClick="FileListView_MouseDoubleClick"
                        RowHeaderWidth="0"
                        RowStyle="{StaticResource DataGridRowStyle}"
                        SelectionMode="{Binding FileSelectionMode, RelativeSource={RelativeSource AncestorType=UserControl}}"
                        dd:DragDrop.DragHandler="{Binding RelativeSource={RelativeSource AncestorType=UserControl}}"
                        dd:DragDrop.DropHandler="{Binding RelativeSource={RelativeSource AncestorType=UserControl}}"
                        dd:DragDrop.IsDragSource="True"
                        dd:DragDrop.IsDropTarget="True"
                        x:Name="FileListView">
                        <DataGrid.ContextMenu>
                            <ContextMenu Opened="ContextMenu_Opened">
                                <MenuItem Click="ContextMenu_Open_Click" Header="打开" />
                                <MenuItem Click="ContextMenu_OpenInNewWindow_Click" Header="在新窗口中打开" />
                                <Separator />
                                <MenuItem
                                    Click="ContextMenu_Extract_Click"
                                    Header="解压到此处"
                                    x:Name="ExtractMenuItem" />
                                <MenuItem
                                    Click="ContextMenu_ExtractTo_Click"
                                    Header="解压到..."
                                    x:Name="ExtractToMenuItem" />
                                <Separator x:Name="ExtractSeparator" />
                                <MenuItem
                                    Click="ContextMenu_CompressToZip_Click"
                                    Header="添加到压缩文件..."
                                    x:Name="CompressMenuItem" />
                                <MenuItem
                                    Click="ContextMenu_CompressToZipHere_Click"
                                    Header="压缩到当前文件夹"
                                    x:Name="CompressHereMenuItem" />
                                <Separator x:Name="CompressSeparator" />
                                <MenuItem Click="ContextMenu_Copy_Click" Header="复制">
                                    <MenuItem.InputGestureText>Ctrl+C</MenuItem.InputGestureText>
                                </MenuItem>
                                <MenuItem Click="ContextMenu_Cut_Click" Header="剪切">
                                    <MenuItem.InputGestureText>Ctrl+X</MenuItem.InputGestureText>
                                </MenuItem>
                                <MenuItem Click="ContextMenu_Paste_Click" Header="粘贴">
                                    <MenuItem.InputGestureText>Ctrl+V</MenuItem.InputGestureText>
                                </MenuItem>
                                <Separator />
                                <MenuItem Click="ContextMenu_Delete_Click" Header="删除">
                                    <MenuItem.InputGestureText>Delete</MenuItem.InputGestureText>
                                </MenuItem>
                                <MenuItem Click="ContextMenu_Rename_Click" Header="重命名">
                                    <MenuItem.InputGestureText>F2</MenuItem.InputGestureText>
                                </MenuItem>
                                <Separator />
                                <MenuItem Click="ContextMenu_Properties_Click" Header="属性" />
                            </ContextMenu>
                        </DataGrid.ContextMenu>
                        <DataGrid.Columns>
                            <!--  名称列  -->
                            <DataGridTemplateColumn
                                CanUserSort="True"
                                Header="名称"
                                MinWidth="200"
                                SortMemberPath="Name"
                                Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate DataType="{x:Type models:FileItem}">
                                        <StackPanel Margin="8,4" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="16"
                                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                Margin="0,0,8,0"
                                                Text="{Binding Icon}"
                                                VerticalAlignment="Center" />
                                            <TextBlock
                                                FontSize="13"
                                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                Text="{Binding Name}"
                                                TextTrimming="CharacterEllipsis"
                                                ToolTip="{Binding Name}"
                                                VerticalAlignment="Center" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  大小列  -->
                            <DataGridTextColumn
                                Binding="{Binding SizeText}"
                                CanUserSort="True"
                                ElementStyle="{StaticResource DataGridTextColumnStyle}"
                                Header="大小"
                                SortMemberPath="SizeText"
                                Width="100" />

                            <!--  类型列  -->
                            <DataGridTextColumn
                                Binding="{Binding Type}"
                                CanUserSort="True"
                                ElementStyle="{StaticResource DataGridTextColumnStyle}"
                                Header="类型"
                                SortMemberPath="Type"
                                Width="100" />

                            <!--  修改日期列  -->
                            <DataGridTextColumn
                                Binding="{Binding ModifiedDate}"
                                CanUserSort="True"
                                ElementStyle="{StaticResource DataGridTextColumnStyle}"
                                Header="修改日期"
                                SortMemberPath="ModifiedDate"
                                Width="150" />
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
        </Grid>

        <!--  状态栏  -->
        <Border
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,1,0,0"
            Grid.Row="4"
            Padding="16,8"
            Visibility="{Binding ShowStatusBar, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Grid.Column="0"
                    Text="{Binding StatusMessage, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    VerticalAlignment="Center" />

                <TextBlock
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Grid.Column="1"
                    Text="{Binding ItemCount, RelativeSource={RelativeSource AncestorType=UserControl}, StringFormat='共 {0} 个项目'}"
                    VerticalAlignment="Center" />
            </Grid>
        </Border>

        <!--  WPF-UI InfoBar for notifications (保留以备后用，但当前隐藏)  -->
        <ui:InfoBar
            HorizontalAlignment="Center"
            IsOpen="False"
            Margin="20"
            MaxWidth="400"
            Severity="Informational"
            Title="通知"
            VerticalAlignment="Bottom"
            Visibility="Collapsed"
            x:Name="NotificationInfoBar" />
    </Grid>
</UserControl>