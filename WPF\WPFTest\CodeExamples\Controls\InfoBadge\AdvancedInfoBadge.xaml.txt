<UserControl x:Class="WPFTest.Examples.AdvancedInfoBadgeExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <UserControl.Resources>
        <!-- 动画资源 -->
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1.0" To="0.3" Duration="0:0:1"
                           AutoReverse="True"/>
        </Storyboard>
        
        <Storyboard x:Key="ScaleAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1.0" To="1.2" Duration="0:0:0.5"
                           AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1.0" To="1.2" Duration="0:0:0.5"
                           AutoReverse="True"/>
        </Storyboard>

        <!-- 自定义样式 -->
        <Style x:Key="AnimatedInfoBadgeStyle" TargetType="ui:InfoBadge">
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsAnimationEnabled}" Value="True">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ScaleAnimation}"/>
                    </DataTrigger.EnterActions>
                    <DataTrigger.ExitActions>
                        <StopStoryboard Storyboard="{StaticResource ScaleAnimation}"/>
                    </DataTrigger.ExitActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <StackPanel>

        <!-- 动画 InfoBadge -->
        <GroupBox Header="动画 InfoBadge">
            <StackPanel>
                
                <!-- 脉冲动画 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="脉冲动画:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="重要通知" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding UrgentCount}" 
                                         Background="Red" 
                                         Foreground="White"
                                         Style="{StaticResource AnimatedInfoBadgeStyle}"/>
                        </ui:Button>
                        <ui:Button Content="新消息">
                            <ui:InfoBadge Background="Orange" Width="12" Height="12">
                                <ui:InfoBadge.Style>
                                    <Style TargetType="ui:InfoBadge">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding HasNewMessages}" Value="True">
                                                <DataTrigger.EnterActions>
                                                    <BeginStoryboard Storyboard="{StaticResource PulseAnimation}"/>
                                                </DataTrigger.EnterActions>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:InfoBadge.Style>
                            </ui:InfoBadge>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 缩放动画 -->
                <StackPanel>
                    <TextBlock Text="缩放动画:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ui:Button Content="动态计数">
                        <ui:InfoBadge Value="{Binding DynamicCount}" 
                                     Background="Blue" 
                                     Foreground="White"
                                     Style="{StaticResource AnimatedInfoBadgeStyle}"/>
                    </ui:Button>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 复杂布局 InfoBadge -->
        <GroupBox Header="复杂布局" Margin="0,16,0,0">
            <StackPanel>
                
                <!-- 多层嵌套 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="多层嵌套:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <Grid>
                        <ui:Button Content="主按钮" Width="120" Height="40">
                            <!-- 主要通知 -->
                            <ui:InfoBadge Value="{Binding MainNotificationCount}" 
                                         Background="Red" 
                                         Foreground="White"
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Top"
                                         Margin="0,-5,-5,0"/>
                        </ui:Button>
                        <!-- 次要状态指示 -->
                        <ui:InfoBadge Background="Green" 
                                     Width="8" Height="8"
                                     HorizontalAlignment="Left"
                                     VerticalAlignment="Bottom"
                                     Margin="10,0,0,5"/>
                    </Grid>
                </StackPanel>

                <!-- 组合控件 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="组合控件:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <!-- 用户头像 + 状态 -->
                        <Border Background="LightBlue" 
                                Width="50" Height="50" 
                                CornerRadius="25"
                                Margin="0,0,16,0">
                            <TextBlock Text="用户" 
                                      HorizontalAlignment="Center" 
                                      VerticalAlignment="Center"
                                      FontWeight="Bold"/>
                            <ui:InfoBadge Background="{Binding UserStatusColor}" 
                                         Width="12" Height="12"
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Bottom"
                                         Margin="0,0,-2,-2"/>
                        </Border>
                        
                        <!-- 文件夹 + 计数 -->
                        <ui:Button Icon="{ui:SymbolIcon Folder24}" 
                                  Content="文档" 
                                  Width="100">
                            <ui:InfoBadge Value="{Binding DocumentCount}" 
                                         Background="Purple" 
                                         Foreground="White"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 条件显示 -->
                <StackPanel>
                    <TextBlock Text="条件显示:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="任务" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding TaskCount}" 
                                         Background="Orange" 
                                         Foreground="White"
                                         Visibility="{Binding HasTasks, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </ui:Button>
                        <ui:Button Content="错误" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Red" Width="10" Height="10"
                                         Visibility="{Binding HasErrors, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </ui:Button>
                        <ui:Button Content="完成">
                            <ui:InfoBadge Background="Green"
                                         Visibility="{Binding IsCompleted, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <ui:SymbolIcon Symbol="Checkmark24" FontSize="10" Foreground="White"/>
                            </ui:InfoBadge>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 自定义样式 InfoBadge -->
        <GroupBox Header="自定义样式" Margin="0,16,0,0">
            <StackPanel>
                
                <!-- 渐变背景 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="渐变背景:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="渐变1" Margin="0,0,8,0">
                            <ui:InfoBadge Value="5" Foreground="White">
                                <ui:InfoBadge.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF6B73FF" Offset="0"/>
                                        <GradientStop Color="#FF9644FF" Offset="1"/>
                                    </LinearGradientBrush>
                                </ui:InfoBadge.Background>
                            </ui:InfoBadge>
                        </ui:Button>
                        <ui:Button Content="渐变2" Margin="0,0,8,0">
                            <ui:InfoBadge Value="12" Foreground="White">
                                <ui:InfoBadge.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FFFF6B6B" Offset="0"/>
                                        <GradientStop Color="#FFFF8E53" Offset="1"/>
                                    </LinearGradientBrush>
                                </ui:InfoBadge.Background>
                            </ui:InfoBadge>
                        </ui:Button>
                        <ui:Button Content="渐变3">
                            <ui:InfoBadge Value="8" Foreground="White">
                                <ui:InfoBadge.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF4ECDC4" Offset="0"/>
                                        <GradientStop Color="#FF44A08D" Offset="1"/>
                                    </LinearGradientBrush>
                                </ui:InfoBadge.Background>
                            </ui:InfoBadge>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 边框样式 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="边框样式:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="边框1" Margin="0,0,8,0">
                            <Border Background="White" 
                                    BorderBrush="Red" 
                                    BorderThickness="2"
                                    CornerRadius="10"
                                    Padding="6,2">
                                <TextBlock Text="{Binding NotificationCount}" 
                                          Foreground="Red" 
                                          FontWeight="Bold"
                                          FontSize="10"/>
                            </Border>
                        </ui:Button>
                        <ui:Button Content="边框2">
                            <Border Background="Transparent" 
                                    BorderBrush="Blue" 
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Padding="4,1">
                                <TextBlock Text="NEW" 
                                          Foreground="Blue" 
                                          FontWeight="Bold"
                                          FontSize="8"/>
                            </Border>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 阴影效果 -->
                <StackPanel>
                    <TextBlock Text="阴影效果:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ui:Button Content="阴影徽章">
                        <ui:InfoBadge Value="{Binding ShadowBadgeCount}" 
                                     Background="DarkBlue" 
                                     Foreground="White">
                            <ui:InfoBadge.Effect>
                                <DropShadowEffect Color="Black" 
                                                Direction="315" 
                                                ShadowDepth="2" 
                                                BlurRadius="4" 
                                                Opacity="0.5"/>
                            </ui:InfoBadge.Effect>
                        </ui:InfoBadge>
                    </ui:Button>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 实际应用场景 -->
        <GroupBox Header="实际应用场景" Margin="0,16,0,0">
            <StackPanel>
                
                <!-- 社交媒体应用 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="社交媒体应用:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="朋友圈" Icon="{ui:SymbolIcon People24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding FriendsCircleCount}" 
                                         Background="Red" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="私信" Icon="{ui:SymbolIcon ChatBubblesQuestion24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding PrivateMessageCount}" 
                                         Background="Green" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="点赞" Icon="{ui:SymbolIcon Heart24}">
                            <ui:InfoBadge Value="{Binding LikeCount}" 
                                         Background="Pink" 
                                         Foreground="White"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 工作协作应用 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="工作协作应用:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="待办" Icon="{ui:SymbolIcon TaskListSquare24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding TodoCount}" 
                                         Background="Orange" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="审批" Icon="{ui:SymbolIcon DocumentCheckmark24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding ApprovalCount}" 
                                         Background="Purple" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="会议" Icon="{ui:SymbolIcon VideoClip24}">
                            <ui:InfoBadge Background="Red" Width="8" Height="8"
                                         Visibility="{Binding HasMeeting, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 电商应用 -->
                <StackPanel>
                    <TextBlock Text="电商应用:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="购物车" Icon="{ui:SymbolIcon Cart24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding CartItemCount}" 
                                         Background="Red" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="收藏" Icon="{ui:SymbolIcon Heart24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding FavoriteCount}" 
                                         Background="Pink" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="优惠券" Icon="{ui:SymbolIcon TicketDiagonal24}">
                            <ui:InfoBadge Value="{Binding CouponCount}" 
                                         Background="Gold" 
                                         Foreground="Black"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

            </StackPanel>
        </GroupBox>

    </StackPanel>
</UserControl>
