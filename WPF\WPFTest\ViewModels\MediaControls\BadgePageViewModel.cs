using System.Collections.ObjectModel;
using System.Reflection;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

using Wpf.Ui.Controls;

namespace WPFTest.ViewModels.MediaControls
{
    /// <summary>
    /// Badge 控件示例页面 ViewModel
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class BadgePageViewModel : ObservableObject
    {
        #region 私有字段

        private readonly YLoggerInstance _logger = YLogger.For<ImagePageViewModel>();

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 BadgePageViewModel 实例
        /// </summary>
        public BadgePageViewModel()
        {
            _logger.Info("BadgePageViewModel 初始化开始");
            
            InitializeCodeExamples();
            SetDefaultValues();
            
            _logger.Info("BadgePageViewModel 初始化完成");
        }

        #endregion

        #region 状态属性

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "欢迎使用 Badge 控件示例！";

        #endregion

        #region Badge 控制属性

        /// <summary>
        /// Badge 内容文本
        /// </summary>
        [ObservableProperty]
        public partial string BadgeContent { get; set; } = "New";

        /// <summary>
        /// Badge 外观样式
        /// </summary>
        [ObservableProperty]
        public partial ControlAppearance BadgeAppearance { get; set; } = ControlAppearance.Primary;

        /// <summary>
        /// 是否显示 Badge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowBadge { get; set; } = true;

        /// <summary>
        /// Badge 宽度
        /// </summary>
        [ObservableProperty]
        public partial double BadgeWidth { get; set; } = 60;

        /// <summary>
        /// Badge 高度
        /// </summary>
        [ObservableProperty]
        public partial double BadgeHeight { get; set; } = 24;

        /// <summary>
        /// 选中的外观样式名称
        /// </summary>
        [ObservableProperty]
        public partial string SelectedAppearanceName { get; set; } = "Primary";

        /// <summary>
        /// 选中的预设内容
        /// </summary>
        [ObservableProperty]
        public partial string SelectedPresetContent { get; set; } = "New";

        #endregion

        #region 演示控制属性

        /// <summary>
        /// 通知计数
        /// </summary>
        [ObservableProperty]
        public partial int NotificationCount { get; set; } = 5;

        /// <summary>
        /// 是否启用动画
        /// </summary>
        [ObservableProperty]
        public partial bool EnableAnimation { get; set; } = true;

        /// <summary>
        /// 是否显示数字 Badge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowNumberBadge { get; set; } = true;

        /// <summary>
        /// 是否显示点状 Badge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowDotBadge { get; set; } = true;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 Badge XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicBadgeXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础 Badge C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicBadgeCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 Badge XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedBadgeXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 Badge C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedBadgeCSharpExample { get; set; } = string.Empty;

        #endregion

        #region 集合属性

        /// <summary>
        /// 外观样式选项
        /// </summary>
        public ObservableCollection<string> AppearanceOptions { get; } = new()
        {
            "Primary", "Secondary", "Success", "Danger", "Caution", "Info", "Light", "Dark", "Transparent"
        };

        /// <summary>
        /// 预设内容选项
        /// </summary>
        public ObservableCollection<string> PresetContentOptions { get; } = new()
        {
            "New", "Hot", "Sale", "VIP", "Beta", "Pro", "Free", "Premium", "Limited", "Popular"
        };

        /// <summary>
        /// 数字内容选项
        /// </summary>
        public ObservableCollection<string> NumberContentOptions { get; } = new()
        {
            "1", "5", "10", "99", "99+", "999", "999+"
        };

        #endregion

        #region 命令

        /// <summary>
        /// 应用外观样式命令
        /// </summary>
        [RelayCommand]
        private void ApplyAppearance()
        {
            try
            {
                BadgeAppearance = SelectedAppearanceName switch
                {
                    "Primary" => ControlAppearance.Primary,
                    "Secondary" => ControlAppearance.Secondary,
                    "Success" => ControlAppearance.Success,
                    "Danger" => ControlAppearance.Danger,
                    "Caution" => ControlAppearance.Caution,
                    "Info" => ControlAppearance.Info,
                    "Light" => ControlAppearance.Light,
                    "Dark" => ControlAppearance.Dark,
                    "Transparent" => ControlAppearance.Transparent,
                    _ => ControlAppearance.Primary
                };

                StatusMessage = $"已应用 {SelectedAppearanceName} 外观样式";
                _logger.Info($"应用外观样式: {SelectedAppearanceName}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"应用外观样式失败: {ex.Message}";
                _logger.Error($"❌ 应用外观样式时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用预设内容命令
        /// </summary>
        [RelayCommand]
        private void ApplyPresetContent()
        {
            try
            {
                BadgeContent = SelectedPresetContent;
                StatusMessage = $"已应用预设内容: {SelectedPresetContent}";
                _logger.Info($"应用预设内容: {SelectedPresetContent}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"应用预设内容失败: {ex.Message}";
                _logger.Error($"❌ 应用预设内容时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 增加通知计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseNotificationCount()
        {
            try
            {
                NotificationCount++;
                StatusMessage = $"通知计数增加到: {NotificationCount}";
                _logger.Info($"通知计数增加到: {NotificationCount}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"增加通知计数失败: {ex.Message}";
                _logger.Error($"❌ 增加通知计数时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 减少通知计数命令
        /// </summary>
        [RelayCommand]
        private void DecreaseNotificationCount()
        {
            try
            {
                if (NotificationCount > 0)
                {
                    NotificationCount--;
                    StatusMessage = $"通知计数减少到: {NotificationCount}";
                    _logger.Info($"通知计数减少到: {NotificationCount}");
                }
                else
                {
                    StatusMessage = "通知计数已经为 0，无法继续减少";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"减少通知计数失败: {ex.Message}";
                _logger.Error($"❌ 减少通知计数时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 重置通知计数命令
        /// </summary>
        [RelayCommand]
        private void ResetNotificationCount()
        {
            try
            {
                NotificationCount = 0;
                StatusMessage = "通知计数已重置为 0";
                _logger.Info("通知计数已重置");
            }
            catch (Exception ex)
            {
                StatusMessage = $"重置通知计数失败: {ex.Message}";
                _logger.Error($"❌ 重置通知计数时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 切换 Badge 显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleBadgeVisibility()
        {
            try
            {
                ShowBadge = !ShowBadge;
                StatusMessage = ShowBadge ? "Badge 已显示" : "Badge 已隐藏";
                _logger.Info($"Badge 显示状态: {ShowBadge}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"切换 Badge 显示失败: {ex.Message}";
                _logger.Error($"❌ 切换 Badge 显示时出错: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置默认值
        /// </summary>
        private void SetDefaultValues()
        {
            try
            {
                // 设置默认的代码示例（如果资源加载失败时使用）
                SetDefaultCodeExamples();
                
                _logger.Info("默认值设置完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 设置默认值时出错: {ex.Message}", ex);
                StatusMessage = "初始化默认值时出错";
            }
        }

        /// <summary>
        /// 设置默认代码示例
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicBadgeXamlExample = """
                <!-- 基础 Badge 示例 -->
                <ui:Badge Appearance="Primary">
                    <TextBlock Text="New" />
                </ui:Badge>
                """;

            BasicBadgeCSharpExample = """
                // 基础 Badge 使用
                var badge = new Badge
                {
                    Appearance = ControlAppearance.Primary,
                    Content = "New"
                };
                """;

            AdvancedBadgeXamlExample = """
                <!-- 高级 Badge 示例 -->
                <ui:Badge Appearance="{Binding BadgeAppearance}"
                         Visibility="{Binding ShowBadge, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding BadgeContent}" />
                </ui:Badge>
                """;

            AdvancedBadgeCSharpExample = """
                // 高级 Badge 使用
                public partial class BadgeViewModel : ObservableObject
                {
                    [ObservableProperty]
                    public partial ControlAppearance BadgeAppearance { get; set; } = ControlAppearance.Primary;
                    
                    [ObservableProperty]
                    public partial string BadgeContent { get; set; } = "New";
                    
                    [ObservableProperty]
                    public partial bool ShowBadge { get; set; } = true;
                }
                """;
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var basePath = "WPFTest.CodeExamples.Controls.Badge.";

                // 加载基础 Badge 示例
                BasicBadgeXamlExample = LoadEmbeddedResource(assembly, basePath + "BasicBadge.xaml.txt");
                BasicBadgeCSharpExample = LoadEmbeddedResource(assembly, basePath + "BasicBadge.cs.txt");

                // 加载高级 Badge 示例
                AdvancedBadgeXamlExample = LoadEmbeddedResource(assembly, basePath + "AdvancedBadge.xaml.txt");
                AdvancedBadgeCSharpExample = LoadEmbeddedResource(assembly, basePath + "AdvancedBadge.cs.txt");

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例时出错: {ex.Message}");
                StatusMessage = "代码示例加载失败，请检查文件是否存在";
            }
        }

        /// <summary>
        /// 加载嵌入式资源
        /// </summary>
        private static string LoadEmbeddedResource(Assembly assembly, string resourceName)
        {
            try
            {
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    return $"// 未找到资源: {resourceName}";
                }

                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                return $"// 加载资源时出错: {ex.Message}";
            }
        }

        #endregion
    }
}
