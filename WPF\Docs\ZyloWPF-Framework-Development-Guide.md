# 🚀 ZyloWPF 框架开发指南

> **现代化 WPF 应用开发的完整解决方案**  
> 基于 WPF-UI + Prism + CommunityToolkit.MVVM 8.4.0 + CodeExampleControl 的最佳实践

---

## 📋 目录

1. [框架概述](#-框架概述)
2. [技术栈](#-技术栈)
3. [项目结构](#-项目结构)
4. [MVVM 模式实现](#-mvvm-模式实现)
5. [控件开发规范](#-控件开发规范)
6. [导航系统](#-导航系统)
7. [代码示例系统](#-代码示例系统)
8. [开发流程](#-开发流程)
9. [最佳实践](#-最佳实践)
10. [常见问题](#-常见问题)

---

## 🎯 框架概述

ZyloWPF 是一个现代化的 WPF 应用开发框架，整合了业界最佳实践和最新技术栈，为开发者提供：

### 🌟 核心特性

- **🎨 现代化 UI**: 基于 WPF-UI 的 Fluent Design 风格
- **🏗️ MVVM 架构**: 使用 CommunityToolkit.MVVM 8.4.0 的最新语法
- **🧭 导航系统**: 基于 Prism 的强大导航和依赖注入
- **📚 示例系统**: 内置 CodeExampleControl 展示代码和效果
- **🔧 开发工具**: 完整的开发模板和代码生成器
- **📖 文档体系**: 详细的开发指南和最佳实践

### 🎯 设计理念

1. **约定优于配置**: 遵循统一的命名和结构约定
2. **组件化开发**: 每个控件都是独立的可复用组件
3. **示例驱动**: 每个控件都包含完整的使用示例
4. **类型安全**: 充分利用 C# 的类型系统和编译时检查
5. **性能优先**: 使用最新的 .NET 特性优化性能

---

## 🛠️ 技术栈

### 核心框架
- **.NET 8.0**: 最新的 .NET 平台
- **WPF**: Windows Presentation Foundation
- **C# 12**: 最新的 C# 语言特性

### UI 框架
- **WPF-UI 3.0+**: 现代化 UI 控件库
- **Fluent Design**: Microsoft 设计语言

### MVVM 框架
- **CommunityToolkit.MVVM 8.4.0**: 微软官方 MVVM 工具包
- **Partial Properties**: 最新的源生成器语法
- **RelayCommand**: 现代化命令实现

### 导航和依赖注入
- **Prism 9.0+**: 企业级 WPF 框架
- **Unity Container**: 依赖注入容器
- **Region Navigation**: 区域导航系统

### 开发工具
- **YLog**: 高性能日志系统
- **CodeExampleControl**: 代码示例展示控件
- **Hot Reload**: 实时代码更新

---

## 📁 项目结构

```
ZyloWPF/
├── 📁 WPF/
│   ├── 📁 Zylo.WPF/                    # 核心框架库
│   │   ├── 📁 Controls/                # 控件库
│   │   │   ├── 📁 Navigation/          # 导航控件
│   │   │   ├── 📁 ButtonControls/      # 按钮控件
│   │   │   ├── 📁 InputControls/       # 输入控件
│   │   │   └── 📁 CodeExample/         # 代码示例控件
│   │   ├── 📁 Models/                  # 数据模型
│   │   ├── 📁 Helpers/                 # 辅助类
│   │   └── 📁 YPrism/                  # Prism 扩展
│   └── 📁 WPFTest/                     # 示例应用
│       ├── 📁 Views/                   # 视图
│       ├── 📁 ViewModels/              # 视图模型
│       ├── 📁 CodeExamples/            # 代码示例
│       └── 📁 Docs/                    # 文档
└── 📁 Docs/                           # 框架文档
```

---

## 🏗️ MVVM 模式实现

### 1. ViewModel 基类

使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法：

```csharp
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace YourApp.ViewModels
{
    /// <summary>
    /// 现代化 ViewModel 基类
    /// </summary>
    public partial class BaseViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 页面标题
        /// </summary>
        [ObservableProperty]
        public partial string PageTitle { get; set; } = string.Empty;

        /// <summary>
        /// 加载状态
        /// </summary>
        [ObservableProperty]
        public partial bool IsLoading { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        [ObservableProperty]
        public partial string ErrorMessage { get; set; } = string.Empty;

        #endregion

        #region 命令

        /// <summary>
        /// 刷新命令
        /// </summary>
        [RelayCommand]
        private async Task RefreshAsync()
        {
            IsLoading = true;
            try
            {
                await LoadDataAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region 虚方法

        /// <summary>
        /// 加载数据（子类重写）
        /// </summary>
        protected virtual Task LoadDataAsync()
        {
            return Task.CompletedTask;
        }

        #endregion
    }
}
```

### 2. 集合属性

```csharp
/// <summary>
/// 集合属性示例
/// </summary>
[ObservableProperty]
public partial ObservableCollection<MenuItemModel> MenuItems { get; set; } = new();

/// <summary>
/// 选中项属性
/// </summary>
[ObservableProperty]
public partial MenuItemModel? SelectedItem { get; set; }
```

### 3. 命令实现

```csharp
/// <summary>
/// 简单命令
/// </summary>
[RelayCommand]
private void SimpleAction()
{
    // 执行操作
}

/// <summary>
/// 带参数命令
/// </summary>
[RelayCommand]
private void ActionWithParameter(string parameter)
{
    // 使用参数执行操作
}

/// <summary>
/// 异步命令
/// </summary>
[RelayCommand]
private async Task AsyncActionAsync()
{
    await SomeAsyncOperation();
}

/// <summary>
/// 条件命令
/// </summary>
[RelayCommand(CanExecute = nameof(CanExecuteAction))]
private void ConditionalAction()
{
    // 执行操作
}

private bool CanExecuteAction() => !IsLoading;
```

---

## 🎛️ 控件开发规范

### 1. 控件结构

每个控件包含以下文件：

```
📁 Controls/Category/
├── 📄 ControlName.xaml           # 控件 XAML
├── 📄 ControlName.xaml.cs        # 控件代码后台
├── 📄 ControlNameModel.cs        # 数据模型（如需要）
└── 📄 README.md                  # 控件说明
```

### 2. 控件 XAML 模板

```xml
<UserControl x:Class="Zylo.WPF.Controls.Category.ControlName"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- 控件样式资源 -->
    </UserControl.Resources>

    <!-- 控件主体 -->
    <Border Background="{DynamicResource ApplicationBackgroundBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}">
        <!-- 控件内容 -->
    </Border>
</UserControl>
```

### 3. 控件代码后台模板

```csharp
using System.Windows;
using System.Windows.Controls;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Controls.Category
{
    /// <summary>
    /// ControlName - 控件描述
    /// </summary>
    public partial class ControlName : UserControl
    {
        #region 日志记录器

        private readonly YLoggerInstance _logger = YLogger.ForSilent<ControlName>();

        #endregion

        #region 依赖属性

        /// <summary>
        /// 示例属性
        /// </summary>
        public static readonly DependencyProperty ExampleProperty =
            DependencyProperty.Register(nameof(Example), typeof(string), 
                typeof(ControlName), new PropertyMetadata(string.Empty));

        /// <summary>
        /// 示例属性
        /// </summary>
        public string Example
        {
            get => (string)GetValue(ExampleProperty);
            set => SetValue(ExampleProperty, value);
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 ControlName
        /// </summary>
        public ControlName()
        {
            InitializeComponent();
            DataContext = this;
            _logger.Info("✅ ControlName 初始化完成");
        }

        #endregion
    }
}
```

---

## 🧭 导航系统

### 1. 页面注册

在 `AppBootstrapper.cs` 中注册页面：

```csharp
protected override void RegisterTypes(IContainerRegistry containerRegistry)
{
    // 注册页面和 ViewModel
    containerRegistry.RegisterForNavigation<Views.ExamplePageView, ViewModels.ExamplePageViewModel>();
}
```

### 2. 导航数据配置

在 `MainViewModel.cs` 中配置导航结构：

```csharp
var navigationData = new NavigationData[]
{
    // 分类
    new("10", "控件分类", "") 
    { 
        WpfUiSymbol = SymbolRegular.Apps24, 
        IsExpanded = true, 
        ParentNumber = null 
    },
    
    // 页面
    new("101", "示例控件", "ExamplePageView") 
    { 
        WpfUiSymbol = SymbolRegular.Document24, 
        ParentNumber = "10" 
    },
};
```

### 3. View 配置

```xml
<UserControl x:Class="WPFTest.Views.ExamplePageView"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:viewModels="clr-namespace:WPFTest.ViewModels"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance viewModels:ExamplePageViewModel}">
    <!-- 页面内容 -->
</UserControl>
```

### 4. ViewModel 配置

```csharp
namespace WPFTest.ViewModels
{
    /// <summary>
    /// 示例页面 ViewModel
    /// 使用 Prism 自动依赖注入
    /// </summary>
    public partial class ExamplePageViewModel : BaseViewModel
    {
        public ExamplePageViewModel()
        {
            PageTitle = "示例页面";
        }
    }
}
```

---

## 📚 代码示例系统

### 1. 页面示例结构

每个控件页面包含：

```xml
<!-- 基础示例 -->
<ui:CardExpander Header="📋 基础示例" IsExpanded="True">
    <StackPanel>
        <!-- 控件演示 -->
        <ui:Card>
            <your:ExampleControl Property="{Binding Value}"/>
        </ui:Card>
        
        <!-- 代码示例 -->
        <codeExample:CodeExampleControl
            Title="基础用法"
            Description="展示控件的基本使用方法"
            ShowTabs="True"
            XamlCode="{Binding BasicXamlExample}"
            CSharpCode="{Binding BasicCSharpExample}"/>
    </StackPanel>
</ui:CardExpander>
```

### 2. 代码示例文件

在 `CodeExamples/Category/ControlName/` 目录下创建：

- `Basic.xaml.txt` - 基础 XAML 示例
- `Basic.cs.txt` - 基础 C# 示例
- `Advanced.xaml.txt` - 高级 XAML 示例
- `Advanced.cs.txt` - 高级 C# 示例

### 3. ViewModel 中的示例代码

```csharp
/// <summary>
/// 初始化代码示例
/// </summary>
private void InitializeCodeExamples()
{
    BasicXamlExample = @"<!-- 基础用法 -->
<your:ExampleControl Property=""Value""/>";

    BasicCSharpExample = @"// 基础用法
var control = new ExampleControl();
control.Property = ""Value"";";
}
```

---

## 🔄 开发流程

### 1. 创建新控件

1. **创建控件文件**
   ```bash
   📁 Zylo.WPF/Controls/Category/
   ├── 📄 NewControl.xaml
   ├── 📄 NewControl.xaml.cs
   └── 📄 NewControlModel.cs (如需要)
   ```

2. **实现控件逻辑**
   - 定义依赖属性
   - 实现控件样式
   - 添加事件处理

3. **创建示例页面**
   ```bash
   📁 WPFTest/Views/Category/
   ├── 📄 NewControlPageView.xaml
   └── 📄 NewControlPageView.xaml.cs
   ```

4. **创建 ViewModel**
   ```bash
   📁 WPFTest/ViewModels/Category/
   └── 📄 NewControlPageViewModel.cs
   ```

5. **创建代码示例**
   ```bash
   📁 WPFTest/CodeExamples/Category/NewControl/
   ├── 📄 Basic.xaml.txt
   ├── 📄 Basic.cs.txt
   ├── 📄 Advanced.xaml.txt
   └── 📄 Advanced.cs.txt
   ```

6. **注册导航**
   - 在 `AppBootstrapper.cs` 中注册页面
   - 在 `MainViewModel.cs` 中添加导航项

### 2. 测试和验证

1. **编译检查**
   ```bash
   dotnet build
   ```

2. **运行测试**
   ```bash
   dotnet run --project WPFTest
   ```

3. **功能验证**
   - 检查控件功能
   - 验证数据绑定
   - 测试主题适配

---

## ✨ 最佳实践

### 1. 命名约定

- **控件**: `PascalCase` (如 `MenuBarControl`)
- **属性**: `PascalCase` (如 `MenuItems`)
- **方法**: `PascalCase` (如 `AddMenuItem`)
- **字段**: `_camelCase` (如 `_logger`)
- **常量**: `UPPER_CASE` (如 `DEFAULT_HEIGHT`)

### 2. 文件组织

- 按功能分类组织文件
- 每个控件独立目录
- 相关文件放在一起

### 3. 代码质量

- 使用 XML 文档注释
- 添加适当的日志记录
- 处理异常情况
- 编写单元测试

### 4. 性能优化

- 使用虚拟化控件处理大数据
- 避免不必要的数据绑定
- 合理使用缓存
- 优化 XAML 结构

### 5. 主题适配

- 使用动态资源
- 支持明暗主题切换
- 遵循 WPF-UI 设计规范

---

## ❓ 常见问题

### Q: 如何添加新的控件分类？

A: 在 `MainViewModel.cs` 的导航数据中添加新的分类节点，并创建对应的目录结构。

### Q: 控件样式如何适配主题？

A: 使用 `{DynamicResource}` 引用 WPF-UI 的主题资源，避免硬编码颜色值。

### Q: 如何处理控件的数据验证？

A: 使用 CommunityToolkit.MVVM 的验证特性，结合 `INotifyDataErrorInfo` 接口。

### Q: 性能问题如何排查？

A: 使用 Visual Studio 的诊断工具，检查内存使用和 CPU 占用。

---

## 📖 相关文档

- [CodeExampleControl 开发文档](./CodeExampleControl开发文档.md)
- [CommunityToolkit MVVM 8.4.0 完整指南](./CommunityToolkit-MVVM-8.4.0-Complete-Guide.md)
- [WPF-UI 控件实现状态](../WPF-UI-Controls-Implementation-Status.md)

---

## 🔧 开发工具和模板

### 1. Visual Studio 代码片段

创建 `.snippet` 文件加速开发：

#### ViewModel 模板
```xml
<CodeSnippets>
  <CodeSnippet Format="1.0.0">
    <Header>
      <Title>ZyloViewModel</Title>
      <Shortcut>zvm</Shortcut>
      <Description>创建 ZyloWPF ViewModel</Description>
    </Header>
    <Snippet>
      <Code Language="csharp">
        <![CDATA[
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace $namespace$.ViewModels.$category$
{
    /// <summary>
    /// $name$ 页面 ViewModel
    /// </summary>
    public partial class $name$ViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 页面标题
        /// </summary>
        [ObservableProperty]
        public partial string PageTitle { get; set; } = "$title$";

        #endregion

        #region 构造函数

        public $name$ViewModel()
        {
            InitializeData();
        }

        #endregion

        #region 命令

        [RelayCommand]
        private void SampleAction()
        {
            // TODO: 实现功能
        }

        #endregion

        #region 私有方法

        private void InitializeData()
        {
            // TODO: 初始化数据
        }

        #endregion
    }
}
        ]]>
      </Code>
    </Snippet>
  </CodeSnippet>
</CodeSnippets>
```

#### 控件模板
```xml
<CodeSnippets>
  <CodeSnippet Format="1.0.0">
    <Header>
      <Title>ZyloControl</Title>
      <Shortcut>zctrl</Shortcut>
      <Description>创建 ZyloWPF 控件</Description>
    </Header>
    <Snippet>
      <Code Language="csharp">
        <![CDATA[
using System.Windows;
using System.Windows.Controls;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Controls.$category$
{
    /// <summary>
    /// $name$ - $description$
    /// </summary>
    public partial class $name$ : UserControl
    {
        #region 日志记录器

        private readonly YLoggerInstance _logger = YLogger.ForSilent<$name$>();

        #endregion

        #region 依赖属性

        // TODO: 添加依赖属性

        #endregion

        #region 构造函数

        public $name$()
        {
            InitializeComponent();
            DataContext = this;
            _logger.Info("✅ $name$ 初始化完成");
        }

        #endregion
    }
}
        ]]>
      </Code>
    </Snippet>
  </CodeSnippet>
</CodeSnippets>
```

### 2. PowerShell 脚本工具

#### 创建新控件脚本
```powershell
# CreateNewControl.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$ControlName,

    [Parameter(Mandatory=$true)]
    [string]$Category,

    [string]$Description = "新控件"
)

$basePath = "WPF"
$controlPath = "$basePath/Zylo.WPF/Controls/$Category"
$viewPath = "$basePath/WPFTest/Views/$Category"
$viewModelPath = "$basePath/WPFTest/ViewModels/$Category"
$examplePath = "$basePath/WPFTest/CodeExamples/$Category/$ControlName"

# 创建目录
New-Item -ItemType Directory -Force -Path $controlPath
New-Item -ItemType Directory -Force -Path $viewPath
New-Item -ItemType Directory -Force -Path $viewModelPath
New-Item -ItemType Directory -Force -Path $examplePath

Write-Host "✅ 已创建控件目录结构: $ControlName" -ForegroundColor Green
```

### 3. EditorConfig 配置

```ini
# .editorconfig
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4

[*.{cs,xaml}]
indent_size = 4

[*.{json,yml,yaml}]
indent_size = 2

[*.cs]
# C# 代码风格
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
```

---

## 📊 性能监控和调试

### 1. 日志系统使用

```csharp
public partial class ExampleViewModel : ObservableObject
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<ExampleViewModel>();

    [RelayCommand]
    private async Task LoadDataAsync()
    {
        _logger.Info("🔄 开始加载数据");

        try
        {
            var stopwatch = Stopwatch.StartNew();

            // 执行操作
            await SomeOperation();

            stopwatch.Stop();
            _logger.InfoDetailed($"✅ 数据加载完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 数据加载失败: {ex.Message}");
            throw;
        }
    }
}
```

### 2. 性能计数器

```csharp
public static class PerformanceCounters
{
    private static readonly Dictionary<string, Stopwatch> _timers = new();

    public static void StartTimer(string name)
    {
        _timers[name] = Stopwatch.StartNew();
    }

    public static long StopTimer(string name)
    {
        if (_timers.TryGetValue(name, out var timer))
        {
            timer.Stop();
            var elapsed = timer.ElapsedMilliseconds;
            _timers.Remove(name);
            return elapsed;
        }
        return 0;
    }
}
```

### 3. 内存使用监控

```csharp
public static class MemoryMonitor
{
    public static void LogMemoryUsage(string context)
    {
        var process = Process.GetCurrentProcess();
        var workingSet = process.WorkingSet64 / 1024 / 1024; // MB
        var privateMemory = process.PrivateMemorySize64 / 1024 / 1024; // MB

        YLogger.ForSilent<MemoryMonitor>()
            .InfoDetailed($"📊 内存使用 [{context}]: 工作集={workingSet}MB, 私有内存={privateMemory}MB");
    }
}
```

---

## 🧪 测试策略

### 1. 单元测试

```csharp
[TestClass]
public class MenuBarViewModelTests
{
    private MenuBarViewModel _viewModel;

    [TestInitialize]
    public void Setup()
    {
        _viewModel = new MenuBarViewModel();
    }

    [TestMethod]
    public void MenuItemClick_ShouldUpdateStatus()
    {
        // Arrange
        var parameter = "TestAction";

        // Act
        _viewModel.MenuItemClickCommand.Execute(parameter);

        // Assert
        Assert.AreEqual($"点击了菜单项: {parameter}", _viewModel.StatusMessage);
        Assert.AreEqual(1, _viewModel.ClickCount);
    }
}
```

### 2. UI 测试

```csharp
[TestClass]
public class MenuBarControlTests
{
    [TestMethod]
    public void MenuBarControl_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var control = new MenuBarControl();

        // Assert
        Assert.IsNotNull(control.MenuItems);
        Assert.AreEqual(40.0, control.MenuHeight);
        Assert.IsTrue(control.IsMenuEnabled);
    }
}
```

---

## 🚀 部署和发布

### 1. 构建配置

```xml
<!-- Directory.Build.props -->
<Project>
  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS8618;CS8625</WarningsNotAsErrors>
  </PropertyGroup>
</Project>
```

### 2. 发布脚本

```powershell
# Publish.ps1
param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "publish"
)

Write-Host "🚀 开始发布应用..." -ForegroundColor Green

# 清理输出目录
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}

# 发布应用
dotnet publish WPF/WPFTest/WPFTest.csproj `
    --configuration $Configuration `
    --output $OutputPath `
    --self-contained true `
    --runtime win-x64

Write-Host "✅ 发布完成: $OutputPath" -ForegroundColor Green
```

---

## 📚 学习资源

### 官方文档
- [WPF-UI 官方文档](https://wpfui.lepo.co/)
- [CommunityToolkit.MVVM 文档](https://docs.microsoft.com/en-us/dotnet/communitytoolkit/mvvm/)
- [Prism 官方文档](https://prismlibrary.com/)

### 示例项目
- [WPF-UI Gallery](https://github.com/lepoco/wpfui)
- [Prism Samples](https://github.com/PrismLibrary/Prism-Samples-Wpf)
- [MVVM Toolkit Samples](https://github.com/CommunityToolkit/dotnet)

### 社区资源
- [WPF 社区](https://github.com/dotnet/wpf)
- [Stack Overflow WPF 标签](https://stackoverflow.com/questions/tagged/wpf)

---

**🎯 持续更新中... 欢迎贡献和反馈！**

> 📧 如有问题或建议，请提交 Issue 或 Pull Request
