using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reflection;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using Zylo.WPF.Models.Navigation;
using Zylo.WPF.Enums;
using Wpf.Ui.Controls;
using Zylo.WPF.Helpers;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.Navigation;

/// <summary>
/// NavigationControl 基础示例 ViewModel
/// </summary>
public partial class NavigationBasicExampleViewModel : ObservableObject
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<NavigationBasicExampleViewModel>();
    private readonly IRegionManager? _regionManager;

    #region 属性

    /// <summary>
    /// 导航项集合
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> NavigationItems { get; set; } = new();

    /// <summary>
    /// 当前选中的导航项
    /// </summary>
    [ObservableProperty]
    public partial ZyloNavigationItemModel? SelectedNavigationItem { get; set; }

    /// <summary>
    /// XAML代码示例
    /// </summary>
    [ObservableProperty]
    public partial string XamlCodeExample { get; set; } = string.Empty;

    /// <summary>
    /// C#代码示例
    /// </summary>
    [ObservableProperty]
    public partial string CSharpCodeExample { get; set; } = string.Empty;

    /// <summary>
    /// 当前导航状态信息
    /// </summary>
    [ObservableProperty]
    public partial string NavigationStatus { get; set; } = "🏠 欢迎使用导航控件演示";

    /// <summary>
    /// 最后导航的目标
    /// </summary>
    [ObservableProperty]
    public partial string LastNavigationTarget { get; set; } = "无";

    #endregion

    #region 构造函数

    /// <summary>
    /// NavigationBasicExampleViewModel 构造函数
    /// </summary>
    /// <param name="regionManager">Prism 区域管理器（依赖注入）</param>
    public NavigationBasicExampleViewModel(IRegionManager? regionManager)
    {
        _regionManager = regionManager!; // 运行时会有值，设计时可能为 null
        InitializeNavigationItems();
        InitializeCodeExamples();
    }

    // 无参构造函数用于设计时
    public NavigationBasicExampleViewModel() : this(null!)
    {
        // 设计时构造函数，_regionManager 将为 null
    }

    #endregion

    #region 命令

    /// <summary>
    /// 导航命令 - 完整的导航功能实现
    /// </summary>
    /// <param name="item">选中的导航项</param>
    [RelayCommand]
    private void NavigateToItem(ZyloNavigationItemModel? item)
    {
        if (item == null) return;

        // 更新选中项
        SelectedNavigationItem = item;

        // 如果有子项目，展开/收缩（不导航）
        if (item.Children?.Count > 0)
        {
            item.IsExpanded = !item.IsExpanded;
            _logger.Info($"📁 {(item.IsExpanded ? "展开" : "收缩")}子菜单 - {item.Name}");
            return; // 有子项目的不进行导航
        }

        // 执行实际导航逻辑
        PerformNavigation(item);
    }

    /// <summary>
    /// 执行实际的导航操作
    /// </summary>
    /// <param name="item">要导航到的项目</param>
    private void PerformNavigation(ZyloNavigationItemModel item)
    {
        try
        {
            // 记录导航操作
            _logger.Info($"🧭 导航到 - {item.Name} (目标: {item.NavigationTarget})");

            // 这里可以实现真正的导航逻辑，例如：
            // 1. Prism 区域导航
            // 2. 页面切换
            // 3. 窗口打开
            // 4. 外部链接跳转

            // 示例：模拟导航到不同的视图
            NavigateToView(item.NavigationTarget, item);

            // 更新导航历史（如果需要）
            UpdateNavigationHistory(item);

        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航失败 - {item.Name}: {ex.Message}");
        }
    }

    /// <summary>
    /// 导航到指定视图
    /// </summary>
    /// <param name="viewName">视图名称</param>
    /// <param name="item">导航项</param>
    private void NavigateToView(string viewName, ZyloNavigationItemModel item)
    {
        string statusMessage;

        // 这里实现具体的视图导航逻辑
        switch (viewName?.ToLower())
        {
            case "homeview":
                statusMessage = "🏠 已导航到首页";
                _logger.Info("🏠 导航到首页");
                break;
            case "settingsview":
                statusMessage = "⚙️ 已导航到设置页面";
                _logger.Info("⚙️ 导航到设置页面");
                break;
            case "aboutview":
                statusMessage = "ℹ️ 已导航到关于页面";
                _logger.Info("ℹ️ 导航到关于页面");
                break;
            default:
                statusMessage = $"📄 已导航到 {item.Name}";
                _logger.Info($"📄 导航到 {viewName ?? "未知页面"}");
                break;
        }

        // 更新导航状态
        NavigationStatus = statusMessage;
        LastNavigationTarget = viewName ?? "未知";

        // 在实际项目中，这里可能是：
        // _regionManager.RequestNavigate("MainRegion", viewName);
        // 或者其他导航框架的调用
    }

    /// <summary>
    /// 更新导航历史
    /// </summary>
    /// <param name="item">导航项</param>
    private void UpdateNavigationHistory(ZyloNavigationItemModel item)
    {
        // 这里可以实现导航历史记录
        _logger.Debug($"📚 添加到导航历史 - {item.Name}");

        // 示例：维护导航历史栈
        // NavigationHistory.Push(item);
    }

    /// <summary>
    /// 刷新导航数据命令 - 重置为初始状态
    /// </summary>
    [RelayCommand]
    private void RefreshNavigation()
    {
        // 简单直接：重新初始化数据并选中第一项
        InitializeNavigationItems();
        _logger.Info("🔄 演示：导航数据已重置为初始状态");
    }



    #region 导航项目操作命令

    /// <summary>
    /// 添加导航项命令
    /// </summary>
    [RelayCommand]
    private void AddNavigationItem()
    {
        if (SelectedNavigationItem?.Children != null)
        {
            AddChildItem(SelectedNavigationItem);
        }
        else
        {
            AddTopLevelItem();
        }
    }



    /// <summary>
    /// 删除选中的导航项
    /// </summary>
    [RelayCommand]
    private void RemoveNavigationItem()
    {
        if (SelectedNavigationItem == null)
        {
            _logger.Warning("⚠️ 没有选中的导航项");
            return;
        }

        var itemToRemove = SelectedNavigationItem;
        var itemName = itemToRemove.Name;

        if (ZyloNavigationItemModel.RemoveItemRecursively(NavigationItems, itemToRemove, OnItemRemoved))
        {
            _logger.Info($"🗑️ 已删除项目 - {itemName}");
        }
        else
        {
            _logger.Warning($"⚠️ 无法找到要删除的项目 - {itemName}");
        }
    }

    /// <summary>
    /// 修改选中的导航项
    /// </summary>
    [RelayCommand]
    private void EditNavigationItem()
    {
        if (SelectedNavigationItem == null)
        {
            _logger.Warning("⚠️ 没有选中的导航项");
            return;
        }

        var originalName = SelectedNavigationItem.Name;

        // 简单的修改示例：在名称前添加"已修改"标记
        if (!SelectedNavigationItem.Name.StartsWith("✏️ "))
        {
            SelectedNavigationItem.Name = $"✏️ {SelectedNavigationItem.Name}";
            _logger.Info($"✏️ 已修改导航项 - {originalName} → {SelectedNavigationItem.Name}");
        }
        else
        {
            // 如果已经修改过，则恢复原名称
            SelectedNavigationItem.Name = SelectedNavigationItem.Name.Substring(3);
            _logger.Info($"🔄 已恢复导航项名称 - {SelectedNavigationItem.Name}");
        }
    }

    /// <summary>
    /// 刷新导航数据
    /// </summary>
    [RelayCommand]
    private void RefreshNavigationData()
    {
        InitializeNavigationItems();
        SelectedNavigationItem = NavigationItems.FirstOrDefault();
        _logger.Info("🔄 导航数据已刷新");
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 添加子项目
    /// </summary>
    private void AddChildItem(ZyloNavigationItemModel parentItem)
    {
        var childNumber = (parentItem.Children!.Count + 1).ToString();
        var newItem = new ZyloNavigationItemModel
        {
            Number = childNumber,
            Name = $"新子项目 {childNumber}",
            NavigationTarget = $"NewChildView{childNumber}",
            ParentNumber = parentItem.Number,
            ZyloSymbol = ZyloSymbol.Project
        };

        parentItem.Children.Add(newItem);
        parentItem.IsExpanded = true;
        SelectedNavigationItem = newItem;

        _logger.Info($"➕ 已添加子项目 - {newItem.Name} (父项目: {parentItem.Name})");
    }

    /// <summary>
    /// 添加顶级项目
    /// </summary>
    private void AddTopLevelItem()
    {
        var topNumber = (NavigationItems.Count + 1).ToString();
        var newItem = new ZyloNavigationItemModel
        {
            Number = topNumber,
            Name = $"新项目 {topNumber}",
            NavigationTarget = $"NewView{topNumber}",
            ParentNumber = null,
            WpfUiSymbol = SymbolRegular.Add24
        };

        NavigationItems.Add(newItem);
        SelectedNavigationItem = newItem;

        _logger.Info($"➕ 已添加顶级项目 - {newItem.Name}");
    }

    /// <summary>
    /// 删除项目后的回调处理 - 智能选择下一个项目
    /// </summary>
    private void OnItemRemoved(int removedIndex, ObservableCollection<ZyloNavigationItemModel> collection)
    {
        if (collection.Count > 0)
        {
            // 智能选择策略：后面 > 前面 > 第一个
            if (removedIndex < collection.Count)
                SelectedNavigationItem = collection[removedIndex];
            else if (removedIndex > 0)
                SelectedNavigationItem = collection[removedIndex - 1];
            else
                SelectedNavigationItem = collection.FirstOrDefault();
        }
        else
        {
            SelectedNavigationItem = null;
        }
    }



    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化导航项 - 基础示例数据
    /// 使用 ParentNumber 方法组装
    /// </summary>
    private void InitializeNavigationItems()
    {
        InitializeByParentNumberMethod();
    }

    /// <summary>
    /// 方法2：使用 ParentNumber 方法组装导航数据
    /// </summary>
    private void InitializeByParentNumberMethod()
    {
        var navigationData = new Zylo.WPF.Helpers.NavigationData[]
        {
            // 顶级项目 - ParentNumber 为 null
            new("1", "首页", "HomePageView") { WpfUiSymbol = SymbolRegular.Home24, ParentNumber = null },
            new("2", "仪表板", "DashboardView") { WpfUiSymbol = SymbolRegular.DataArea24, ParentNumber = null },
            new("3", "用户管理", "") { WpfUiSymbol = SymbolRegular.People24, IsExpanded = true, ParentNumber = null },
            new("4", "系统设置", "") { WpfUiSymbol = SymbolRegular.Settings24, IsExpanded = false, ParentNumber = null },
            new("5", "关于", "AboutView") { Emoji = "ℹ️", ParentNumber = null },

            // 子项目 - 通过 ParentNumber 指定父项目
            new("6", "用户列表", "UsersView") { ZyloSymbol = ZyloSymbol.Project, ParentNumber = "3" },
            new("7", "角色管理", "RolesView") { ZyloSymbol = ZyloSymbol.Buildings, ParentNumber = "3" },
            new("8", "基本设置", "SettingsView") { ZyloSymbol = ZyloSymbol.ICO, ParentNumber = "4" },
            new("9", "高级设置", "AdvancedSettingsView") { ZyloSymbol = ZyloSymbol.Notes, ParentNumber = "4" },
        };

        // 使用 AssembleByParentNumber 方法组装
        NavigationItems = NavigationItemHelper.AssembleByParentNumber(navigationData);

        // 调试：检查组装后的数据结构
        foreach (var item in NavigationItems)
        {
            _logger.Debug($"顶级项目: {item.Name}, 子项目数量: {item.Children?.Count ?? 0}");
            if (item.Children != null && item.Children.Count > 0)
            {
                foreach (var child in item.Children)
                {
                    _logger.Debug($"  - 子项目: {child.Name} (ParentNumber: {child.ParentNumber})");
                }
            }
        }

        SelectedNavigationItem = NavigationItems.FirstOrDefault();
    }

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamples()
    {
        try
        {
            // 获取代码示例文件的基础路径
            var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Navigation", "NavigationControl");

            // 读取基础导航示例
            XamlCodeExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
            CSharpCodeExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));

            _logger.Info("代码示例加载完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"加载代码示例失败: {ex.Message}");

            // 如果文件读取失败，使用默认示例
            SetDefaultCodeExamples();
        }
    }

    /// <summary>
    /// 读取代码示例文件
    /// </summary>
    private string ReadCodeExampleFile(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                return File.ReadAllText(filePath);
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"<!-- 文件不存在: {Path.GetFileName(filePath)} -->";
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"读取代码示例文件失败: {ex.Message}");
            return $"<!-- 读取失败: {Path.GetFileName(filePath)} -->";
        }
    }

    /// <summary>
    /// 设置默认代码示例（当文件读取失败时使用）
    /// </summary>
    private void SetDefaultCodeExamples()
    {
        XamlCodeExample = @"
<!-- NavigationControl 完整功能用法 -->
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width=""Auto""/>
        <ColumnDefinition Width=""5""/>
        <ColumnDefinition Width=""*""/>
    </Grid.ColumnDefinitions>

    <!-- 左侧：NavigationControl 演示 -->
    <ui:Card Grid.Column=""0"" Padding=""15"" MinWidth=""300"">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height=""Auto""/>
                <RowDefinition Height=""*""/>
            </Grid.RowDefinitions>

            <!-- 导航标题 -->
            <TextBlock Grid.Row=""0""
                      Text=""📱 基础导航菜单""
                      FontSize=""16""
                      FontWeight=""SemiBold""
                      Margin=""0,0,0,15""/>

            <!-- NavigationControl -->
            <zylo:NavigationControl Grid.Row=""1""
                                   ItemsSource=""{Binding NavigationItems}""
                                   SelectedItem=""{Binding SelectedNavigationItem, Mode=TwoWay}""
                                   NavigationItemSelectedCommand=""{Binding NavigationCommand}""
                                   ShowSubMenuOnClick=""True""
                                   Background=""Transparent""/>
        </Grid>
    </ui:Card>

    <!-- 分隔符 -->
    <GridSplitter Grid.Column=""1""
                 Width=""5""
                 HorizontalAlignment=""Stretch""
                 VerticalAlignment=""Stretch""
                 Background=""{DynamicResource ControlStrokeColorDefaultBrush}""
                 ResizeBehavior=""PreviousAndNext""
                 ResizeDirection=""Columns""/>

    <!-- 右侧：功能演示区域 -->
    <ui:Card Grid.Column=""2"" Padding=""15"">
        <StackPanel>
            <!-- CRUD 操作按钮 -->
            <TextBlock Text=""⚡ 功能演示"" FontWeight=""SemiBold"" Margin=""0,0,0,10""/>

            <ui:Button Content=""🔄 刷新导航数据""
                      Command=""{Binding RefreshNavigationDataCommand}""
                      Margin=""0,5""/>

            <ui:Button Content=""➕ 添加导航项""
                      Command=""{Binding AddNavigationItemCommand}""
                      Margin=""0,5""/>

            <ui:Button Content=""✏️ 修改选中项""
                      Command=""{Binding EditNavigationItemCommand}""
                      Margin=""0,5""/>

            <ui:Button Content=""🗑️ 移除选中项""
                      Command=""{Binding RemoveNavigationItemCommand}""
                      Margin=""0,5""/>
        </StackPanel>
    </ui:Card>
</Grid>

<!-- 命名空间声明 -->
<UserControl xmlns:zylo=""clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF""
             xmlns:ui=""http://schemas.lepo.co/wpfui/2022/xaml""
             xmlns:prism=""http://prismlibrary.com/"">
    <!-- 控件内容 -->
</UserControl>";

        // C# 代码示例 - 最新的完整 CRUD 功能实现
        CSharpCodeExample = @"
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.WPF.Models.Navigation;
using Wpf.Ui.Controls;

// NavigationControl 完整功能演示 ViewModel
public partial class NavigationBasicExampleViewModel : ObservableObject
{
    #region 属性

    // 导航项集合
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> NavigationItems { get; set; } = new();

    // 当前选中的导航项
    [ObservableProperty]
    public partial ZyloNavigationItemModel? SelectedNavigationItem { get; set; }

    #endregion

    #region 导航项目操作命令

    /// <summary>
    /// 添加导航项命令
    /// </summary>
    [RelayCommand]
    private void AddNavigationItem()
    {
        if (SelectedNavigationItem?.Children != null)
        {
            // 添加子项目
            AddChildItem(SelectedNavigationItem);
        }
        else
        {
            // 添加顶级项目
            AddTopLevelItem();
        }
    }

    /// <summary>
    /// 删除选中的导航项
    /// </summary>
    [RelayCommand]
    private void RemoveNavigationItem()
    {
        if (SelectedNavigationItem == null) return;

        var itemToRemove = SelectedNavigationItem;

        // 使用 ZyloNavigationItemModel 的静态帮助方法递归删除
        if (ZyloNavigationItemModel.RemoveItemRecursively(NavigationItems, itemToRemove, OnItemRemoved))
        {
            // 删除成功
        }
    }

    /// <summary>
    /// 修改选中的导航项
    /// </summary>
    [RelayCommand]
    private void EditNavigationItem()
    {
        if (SelectedNavigationItem == null) return;

        // 简单的修改示例：更新名称
        SelectedNavigationItem.Name = $""已修改 - {SelectedNavigationItem.Name}"";
    }

    /// <summary>
    /// 刷新导航数据
    /// </summary>
    [RelayCommand]
    private void RefreshNavigationData()
    {
        InitializeNavigationItems();
        SelectedNavigationItem = NavigationItems.FirstOrDefault();
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 添加子项目
    /// </summary>
    private void AddChildItem(ZyloNavigationItemModel parentItem)
    {
        var childNumber = (parentItem.Children!.Count + 1).ToString();
        var newItem = new ZyloNavigationItemModel
        {
            Number = childNumber,
            Name = $""新子项目 {childNumber}"",
            NavigationTarget = $""NewChildView{childNumber}"",
            ParentNumber = parentItem.Number,
            ZyloSymbol = ZyloSymbol.Project
        };

        parentItem.Children.Add(newItem);
        parentItem.IsExpanded = true;
        SelectedNavigationItem = newItem;
    }

    /// <summary>
    /// 添加顶级项目
    /// </summary>
    private void AddTopLevelItem()
    {
        var topNumber = (NavigationItems.Count + 1).ToString();
        var newItem = new ZyloNavigationItemModel
        {
            Number = topNumber,
            Name = $""新项目 {topNumber}"",
            NavigationTarget = $""NewView{topNumber}"",
            ParentNumber = null,
            WpfUiSymbol = SymbolRegular.Add24
        };

        NavigationItems.Add(newItem);
        SelectedNavigationItem = newItem;
    }

    /// <summary>
    /// 删除项目后的回调处理
    /// </summary>
    private void OnItemRemoved(int removedIndex, ObservableCollection<ZyloNavigationItemModel> collection)
    {
        if (collection.Count > 0)
        {
            // 智能选择策略：后面 > 前面 > 第一个
            if (removedIndex < collection.Count)
                SelectedNavigationItem = collection[removedIndex];
            else if (removedIndex > 0)
                SelectedNavigationItem = collection[removedIndex - 1];
            else
                SelectedNavigationItem = collection.FirstOrDefault();
        }
        else
        {
            SelectedNavigationItem = null;
        }
    }

    #endregion
}";

        #endregion

        // ================================================================
        // 📚 NavigationBasicExampleViewModel 基础示例总结
        // ================================================================
        //
        // 🎯 主要功能：
        // ✅ NavigationControl 基础用法演示
        // ✅ 简单的导航数据管理
        // ✅ 基础导航命令处理
        // ✅ XAML 和 C# 代码示例展示
        //
        // 🏗️ 技术特点：
        // 📐 使用 CommunityToolkit.Mvvm 实现 MVVM 模式
        // 🎯 通过 RelayCommand 处理用户交互
        // 📋 使用 ObservableProperty 实现属性通知
        // � 参考 MainViewModel 的最佳实践
        //
        // 💡 设计目标：
        // � 作为 NavigationControl 的入门教学示例
        // � 展示控件的核心功能和基本用法
        // � 提供清晰易懂的代码结构
        // � 为进阶功能学习打下基础
        //
        // ================================================================
    }

    #endregion
}
