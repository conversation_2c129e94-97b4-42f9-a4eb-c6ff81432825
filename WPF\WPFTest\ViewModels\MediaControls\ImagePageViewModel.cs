using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.MediaControls
{
    /// <summary>
    /// Image 控件页面的 ViewModel，演示各种图片功能
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class ImagePageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<ImagePageViewModel>();
        private readonly DispatcherTimer _carouselTimer;
        private int _currentImageIndex = 0;

        #region 基础属性 - 使用 Partial Properties 语法

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "欢迎使用 Image 控件示例！选择图片开始体验。";

        /// <summary>
        /// 当前选中的图片路径
        /// </summary>
        [ObservableProperty]
        public partial string SelectedImagePath { get; set; } = "pack://application:,,,/Assets/Images/sample.jpg";

        /// <summary>
        /// 当前拉伸模式
        /// </summary>
        [ObservableProperty]
        public partial Stretch CurrentStretch { get; set; } = Stretch.Uniform;

        /// <summary>
        /// 图片宽度
        /// </summary>
        [ObservableProperty]
        public partial double ImageWidth { get; set; } = 200;

        /// <summary>
        /// 图片高度
        /// </summary>
        [ObservableProperty]
        public partial double ImageHeight { get; set; } = 150;

        /// <summary>
        /// 是否显示边框
        /// </summary>
        [ObservableProperty]
        public partial bool ShowBorder { get; set; } = false;

        /// <summary>
        /// 边框厚度
        /// </summary>
        [ObservableProperty]
        public partial Thickness BorderThickness { get; set; } = new Thickness(2);

        /// <summary>
        /// 边框颜色
        /// </summary>
        [ObservableProperty]
        public partial Brush BorderBrush { get; set; } = Brushes.DodgerBlue;

        #endregion

        #region 轮播相关属性

        /// <summary>
        /// 当前轮播图片
        /// </summary>
        [ObservableProperty]
        public partial string CurrentCarouselImage { get; set; } = string.Empty;

        /// <summary>
        /// 是否自动播放轮播
        /// </summary>
        [ObservableProperty]
        public partial bool IsAutoPlay { get; set; } = true;

        /// <summary>
        /// 播放/暂停按钮文本
        /// </summary>
        [ObservableProperty]
        public partial string PlayPauseButtonText { get; set; } = "⏸";

        /// <summary>
        /// 自动播放状态变化时更新按钮文本
        /// </summary>
        partial void OnIsAutoPlayChanged(bool value)
        {
            PlayPauseButtonText = value ? "⏸" : "▶";
        }

        /// <summary>
        /// 轮播间隔（秒）
        /// </summary>
        [ObservableProperty]
        public partial int CarouselInterval { get; set; } = 3;

        /// <summary>
        /// 轮播间隔变化时更新定时器
        /// </summary>
        partial void OnCarouselIntervalChanged(int value)
        {
            if (_carouselTimer != null)
            {
                _carouselTimer.Interval = TimeSpan.FromSeconds(value);
                _logger.Info($"轮播间隔已更新为: {value} 秒");
            }
        }

        /// <summary>
        /// 当前图片索引
        /// </summary>
        [ObservableProperty]
        public partial int CurrentImageIndex { get; set; } = 0;

        #endregion

        #region 特效相关属性

        /// <summary>
        /// 当前特效类型
        /// </summary>
        [ObservableProperty]
        public partial string CurrentEffectType { get; set; } = "None";

        /// <summary>
        /// 模糊半径
        /// </summary>
        [ObservableProperty]
        public partial double BlurRadius { get; set; } = 5.0;

        /// <summary>
        /// 阴影深度
        /// </summary>
        [ObservableProperty]
        public partial double ShadowDepth { get; set; } = 5.0;

        /// <summary>
        /// 阴影方向
        /// </summary>
        [ObservableProperty]
        public partial double ShadowDirection { get; set; } = 315.0;

        /// <summary>
        /// 阴影透明度
        /// </summary>
        [ObservableProperty]
        public partial double ShadowOpacity { get; set; } = 0.5;

        /// <summary>
        /// 是否启用动画
        /// </summary>
        [ObservableProperty]
        public partial bool IsAnimationEnabled { get; set; } = true;

        /// <summary>
        /// 动画持续时间（秒）
        /// </summary>
        [ObservableProperty]
        public partial double AnimationDuration { get; set; } = 2.0;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础图片 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicImageXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础图片 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicImageCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级图片 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedImageXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级图片 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedImageCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 图片特效 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string ImageEffectsXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 图片特效 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string ImageEffectsCSharpExample { get; set; } = string.Empty;

        #endregion

        #region 集合属性

        /// <summary>
        /// 轮播图片集合 - 使用本地示例图片
        /// </summary>
        public ObservableCollection<string> CarouselImages { get; } = new()
        {
            "pack://application:,,,/Assets/Images/carousel1.jpg",
            "pack://application:,,,/Assets/Images/carousel2.jpg",
            "pack://application:,,,/Assets/Images/carousel3.jpg",
            "pack://application:,,,/Assets/Images/carousel4.jpg",
            "pack://application:,,,/Assets/Images/carousel5.jpg"
        };

        /// <summary>
        /// 图片画廊集合
        /// </summary>
        public ObservableCollection<string> GalleryImages { get; } = new()
        {
            "pack://application:,,,/Assets/Images/gallery1.jpg",
            "pack://application:,,,/Assets/Images/gallery2.jpg",
            "pack://application:,,,/Assets/Images/gallery3.jpg",
            "pack://application:,,,/Assets/Images/gallery4.jpg",
            "pack://application:,,,/Assets/Images/gallery5.jpg",
            "pack://application:,,,/Assets/Images/gallery6.jpg"
        };

        /// <summary>
        /// 拉伸模式选项
        /// </summary>
        public ObservableCollection<string> StretchModes { get; } = new()
        {
            "None", "Fill", "Uniform", "UniformToFill"
        };

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 ImagePageViewModel
        /// </summary>
        public ImagePageViewModel()
        {
            // 初始化轮播定时器
            _carouselTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(CarouselInterval)
            };
            _carouselTimer.Tick += CarouselTimer_Tick;

            // 设置初始图片
            if (CarouselImages.Count > 0)
            {
                CurrentCarouselImage = CarouselImages[0];
            }

            // 开始自动播放
            if (IsAutoPlay)
            {
                _carouselTimer.Start();
            }

            StatusMessage = "Image 控件示例已加载，开始探索各种图片功能！";
            InitializeCodeExamples();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 选择图片命令
        /// </summary>
        [RelayCommand]
        private void SelectImage()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择图片文件",
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff|所有文件|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                SelectedImagePath = openFileDialog.FileName;
                StatusMessage = $"已选择图片: {Path.GetFileName(openFileDialog.FileName)}";
                _logger.Info($"用户选择了图片: {openFileDialog.FileName}");
            }
        }

        /// <summary>
        /// 设置拉伸模式命令
        /// </summary>
        [RelayCommand]
        private void SetStretchMode(string? stretchMode)
        {
            if (string.IsNullOrEmpty(stretchMode)) return;

            CurrentStretch = stretchMode switch
            {
                "None" => Stretch.None,
                "Fill" => Stretch.Fill,
                "Uniform" => Stretch.Uniform,
                "UniformToFill" => Stretch.UniformToFill,
                _ => Stretch.Uniform
            };

            StatusMessage = $"拉伸模式已设置为: {CurrentStretch}";
            _logger.Info($"拉伸模式已更改为: {CurrentStretch}");
        }

        /// <summary>
        /// 重置图片大小命令
        /// </summary>
        [RelayCommand]
        private void ResetImageSize()
        {
            ImageWidth = 200;
            ImageHeight = 150;
            StatusMessage = "图片大小已重置为默认值";
            _logger.Info("图片大小已重置");
        }

        /// <summary>
        /// 切换边框显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleBorder()
        {
            ShowBorder = !ShowBorder;
            StatusMessage = ShowBorder ? "已显示边框" : "已隐藏边框";
            _logger.Info($"边框显示状态: {ShowBorder}");
        }

        /// <summary>
        /// 下一张图片命令
        /// </summary>
        [RelayCommand]
        private void NextImage()
        {
            if (CarouselImages.Count == 0) return;

            _currentImageIndex = (_currentImageIndex + 1) % CarouselImages.Count;
            CurrentCarouselImage = CarouselImages[_currentImageIndex];
            CurrentImageIndex = _currentImageIndex;

            StatusMessage = $"切换到第 {_currentImageIndex + 1} 张图片";
            _logger.Info($"轮播切换到图片索引: {_currentImageIndex}");
        }

        /// <summary>
        /// 上一张图片命令
        /// </summary>
        [RelayCommand]
        private void PreviousImage()
        {
            if (CarouselImages.Count == 0) return;

            _currentImageIndex = _currentImageIndex == 0
                ? CarouselImages.Count - 1
                : _currentImageIndex - 1;

            CurrentCarouselImage = CarouselImages[_currentImageIndex];
            CurrentImageIndex = _currentImageIndex;

            StatusMessage = $"切换到第 {_currentImageIndex + 1} 张图片";
            _logger.Info($"轮播切换到图片索引: {_currentImageIndex}");
        }

        /// <summary>
        /// 切换自动播放命令
        /// </summary>
        [RelayCommand]
        private void ToggleAutoPlay()
        {
            IsAutoPlay = !IsAutoPlay;

            if (IsAutoPlay)
            {
                _carouselTimer.Start();
                StatusMessage = "已开启自动播放";
            }
            else
            {
                _carouselTimer.Stop();
                StatusMessage = "已关闭自动播放";
            }

            _logger.Info($"自动播放状态: {IsAutoPlay}");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 轮播定时器事件
        /// </summary>
        private void CarouselTimer_Tick(object? sender, EventArgs e)
        {
            NextImage();
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var basePath = "WPFTest.CodeExamples.Controls.Image.";

                // 加载基础图片示例
                BasicImageXamlExample = LoadEmbeddedResource(assembly, basePath + "BasicImage.xaml.txt");
                BasicImageCSharpExample = LoadEmbeddedResource(assembly, basePath + "BasicImage.cs.txt");

                // 加载高级图片示例
                AdvancedImageXamlExample = LoadEmbeddedResource(assembly, basePath + "AdvancedImage.xaml.txt");
                AdvancedImageCSharpExample = LoadEmbeddedResource(assembly, basePath + "AdvancedImage.cs.txt");

                // 加载图片特效示例
                ImageEffectsXamlExample = LoadEmbeddedResource(assembly, basePath + "ImageEffects.xaml.txt");
                ImageEffectsCSharpExample = LoadEmbeddedResource(assembly, basePath + "ImageEffects.cs.txt");

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例时出错: {ex.Message}");
                StatusMessage = "代码示例加载失败，请检查文件是否存在";
            }
        }

        /// <summary>
        /// 加载嵌入式资源
        /// </summary>
        private static string LoadEmbeddedResource(Assembly assembly, string resourceName)
        {
            try
            {
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    return $"// 未找到资源: {resourceName}";
                }

                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                return $"// 加载资源时出错: {ex.Message}";
            }
        }

        #endregion
    }
}
