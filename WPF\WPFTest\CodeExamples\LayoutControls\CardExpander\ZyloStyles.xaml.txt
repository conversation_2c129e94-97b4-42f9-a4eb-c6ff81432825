<!-- <PERSON><PERSON><PERSON> CardExpander 样式使用示例 -->
<StackPanel Margin="20">

    <!-- 基础样式展示 -->
    <TextBlock Text="🎨 Zylo CardExpander 样式库" 
               FontSize="18" 
               FontWeight="Bold" 
               Margin="0,0,0,16"/>

    <!-- 标准卡片样式 -->
    <ui:CardExpander Header="标准卡片样式" 
                     Style="{StaticResource ZyloCardStyle}"
                     IsExpanded="True">
        <StackPanel Margin="16">
            <TextBlock Text="这是使用 ZyloCardStyle 的标准卡片。" 
                       FontWeight="Medium" 
                       Margin="0,0,0,8"/>
            <TextBlock Text="继承了 WPF-UI 的基础样式，并添加了微妙的阴影效果和悬停交互。" 
                       TextWrapping="Wrap"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 紧凑卡片样式 -->
    <ui:CardExpander Header="紧凑卡片样式" 
                     Style="{StaticResource ZyloCompactCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="12">
            <TextBlock Text="紧凑样式，适合空间有限的场景。" 
                       FontSize="12"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 大型卡片样式 -->
    <ui:CardExpander Header="大型卡片样式" 
                     Style="{StaticResource ZyloLargeCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="24">
            <TextBlock Text="大型样式，适合重要内容展示。" 
                       FontSize="16" 
                       FontWeight="Medium" 
                       Margin="0,0,0,12"/>
            <TextBlock Text="具有更大的内边距和更明显的阴影效果。" 
                       TextWrapping="Wrap"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 强调卡片样式 -->
    <ui:CardExpander Header="强调卡片样式" 
                     Style="{StaticResource ZyloAccentCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="16">
            <TextBlock Text="使用主题强调色的卡片样式。" 
                       FontWeight="Medium"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 现代化卡片样式 -->
    <ui:CardExpander Header="现代化卡片样式" 
                     Style="{StaticResource ZyloModernCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="16">
            <TextBlock Text="无边框设计，强调阴影效果的现代化样式。" 
                       FontWeight="Medium"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 状态样式展示 -->
    <TextBlock Text="📊 状态样式" 
               FontSize="16" 
               FontWeight="Bold" 
               Margin="0,24,0,16"/>

    <!-- 信息样式 -->
    <ui:CardExpander Header="ℹ️ 信息卡片" 
                     Style="{StaticResource ZyloInfoCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="16">
            <TextBlock Text="用于显示信息性内容的卡片样式。"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 成功样式 -->
    <ui:CardExpander Header="✅ 成功卡片" 
                     Style="{StaticResource ZyloSuccessCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="16">
            <TextBlock Text="用于显示成功状态的卡片样式。"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 警告样式 -->
    <ui:CardExpander Header="⚠️ 警告卡片" 
                     Style="{StaticResource ZyloWarningCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="16">
            <TextBlock Text="用于显示警告信息的卡片样式。"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 错误样式 -->
    <ui:CardExpander Header="❌ 错误卡片" 
                     Style="{StaticResource ZyloErrorCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="16">
            <TextBlock Text="用于显示错误信息的卡片样式。"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 专用样式展示 -->
    <TextBlock Text="🛠️ 专用样式" 
               FontSize="16" 
               FontWeight="Bold" 
               Margin="0,24,0,16"/>

    <!-- 代码示例样式 -->
    <ui:CardExpander Header="代码示例卡片" 
                     Style="{StaticResource ZyloCodeExampleCardStyle}"
                     IsExpanded="False">
        <StackPanel Margin="20">
            <TextBlock Text="// 专为代码展示优化的样式" 
                       FontFamily="Consolas" 
                       FontSize="12"/>
            <TextBlock Text="var example = new CodeExample();" 
                       FontFamily="Consolas" 
                       FontSize="12" 
                       Margin="0,4,0,0"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 演示样式 -->
    <ui:CardExpander Header="演示卡片" 
                     Style="{StaticResource ZyloDemoCardStyle}"
                     IsExpanded="True">
        <StackPanel Margin="16">
            <TextBlock Text="专为功能演示优化的卡片样式。" 
                       FontWeight="Medium" 
                       Margin="0,0,0,8"/>
            <TextBlock Text="默认展开，适合在示例页面中使用。" 
                       TextWrapping="Wrap"/>
        </StackPanel>
    </ui:CardExpander>

</StackPanel>
