# 🎯 CommunityToolkit.Mvvm 完整标签汇总指南

## 📋 核心组件架构

CommunityToolkit.Mvvm 是微软官方维护的现代化 MVVM 框架，基于 **Source Generators** 技术，支持 .NET Standard 2.0/2.1 和 .NET 6+，适用于所有 UI 框架（WPF、WinUI、UWP、MAUI、Avalonia 等）。

---

## 🏷️ 1. 属性相关标签 (ObservableProperty)

### **`[ObservableProperty]`** - 核心属性标签
```csharp
[ObservableProperty]
private string name = string.Empty;
// 自动生成: public string Name { get; set; }
```

### **`[NotifyPropertyChangedFor]`** - 通知依赖属性
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(FullName))]
[NotifyPropertyChangedFor(nameof(DisplayText))]
private string firstName = string.Empty;

public string FullName => $"{FirstName} {LastName}";
```

### **`[NotifyCanExecuteChangedFor]`** - 通知命令状态
```csharp
[ObservableProperty]
[NotifyCanExecuteChangedFor(nameof(SaveCommand))]
[NotifyCanExecuteChangedFor(nameof(DeleteCommand))]
private bool isDataValid;
```

### **`[NotifyDataErrorInfo]`** - 启用验证
```csharp
[ObservableProperty]
[NotifyDataErrorInfo]
[Required]
[MinLength(2)]
[MaxLength(50)]
private string userName = string.Empty;
```

### **`[NotifyPropertyChangedRecipients]`** - 发送消息
```csharp
[ObservableProperty]
[NotifyPropertyChangedRecipients]
private string selectedItem = string.Empty;
// 自动发送 PropertyChangedMessage<T>
```

### **`[property:]`** - 转发自定义属性标签
```csharp
[ObservableProperty]
[property: JsonPropertyName("user_name")]
[property: JsonRequired]
[property: Description("用户名称")]
private string userName = string.Empty;
```

---

## 🏷️ 2. 命令相关标签 (RelayCommand)

### **`[RelayCommand]`** - 基础命令标签
```csharp
[RelayCommand]
private void SaveData()
{
    // 同步命令逻辑
}
// 自动生成: public IRelayCommand SaveDataCommand { get; }
```

### **`[RelayCommand]`** - 带参数命令
```csharp
[RelayCommand]
private void DeleteItem(string itemId)
{
    // 带参数的命令
}
// 自动生成: public IRelayCommand<string> DeleteItemCommand { get; }
```

### **`[RelayCommand]`** - 异步命令
```csharp
[RelayCommand]
private async Task LoadDataAsync()
{
    // 异步命令逻辑
    await Task.Delay(1000);
}
// 自动生成: public IAsyncRelayCommand LoadDataCommand { get; }
```

### **`[RelayCommand]`** - 带取消令牌的异步命令
```csharp
[RelayCommand]
private async Task ProcessDataAsync(CancellationToken cancellationToken)
{
    // 支持取消的异步命令
    await SomeOperationAsync(cancellationToken);
}
```

### **`[RelayCommand(CanExecute = "方法名")]`** - 指定 CanExecute
```csharp
[RelayCommand(CanExecute = nameof(CanSaveData))]
private void SaveData()
{
    // 保存逻辑
}

private bool CanSaveData()
{
    return !string.IsNullOrEmpty(UserName);
}
```

### **`[RelayCommand(AllowConcurrentExecutions = true)]`** - 允许并发执行
```csharp
[RelayCommand(AllowConcurrentExecutions = true)]
private async Task DownloadFileAsync()
{
    // 允许多个下载同时进行
}
```

### **`[RelayCommand(FlowExceptionsToTaskScheduler = true)]`** - 异常处理
```csharp
[RelayCommand(FlowExceptionsToTaskScheduler = true)]
private async Task RiskyOperationAsync()
{
    // 异常不会崩溃应用，而是流向 TaskScheduler
}
```

### **`[RelayCommand(IncludeCancelCommand = true)]`** - 生成取消命令
```csharp
[RelayCommand(IncludeCancelCommand = true)]
private async Task LongRunningTaskAsync(CancellationToken token)
{
    // 长时间运行的任务
}
// 自动生成: 
// - public IAsyncRelayCommand LongRunningTaskCommand { get; }
// - public ICommand LongRunningTaskCancelCommand { get; }
```

### **`[property:]`** - 转发自定义命令标签
```csharp
[RelayCommand]
[property: JsonIgnore]
[property: Description("保存数据命令")]
private void SaveData()
{
    // 命令逻辑
}
```

---

## 🏷️ 3. 基类相关标签

### **继承基类选择**
```csharp
// 基础通知
public partial class MyViewModel : ObservableObject

// 带验证功能
public partial class MyViewModel : ObservableValidator

// 带消息功能
public partial class MyViewModel : ObservableRecipient
```

### **`[INotifyPropertyChanged]`** - 自动实现接口
```csharp
[INotifyPropertyChanged]
public partial class MyModel
{
    [ObservableProperty]
    private string name = string.Empty;
}
// 自动实现 INotifyPropertyChanged 接口
```

---

## 🏷️ 4. 验证相关标签

### **数据验证标签组合**
```csharp
[ObservableProperty]
[NotifyDataErrorInfo]
[Required(ErrorMessage = "用户名不能为空")]
[StringLength(20, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-20之间")]
[RegularExpression(@"^[a-zA-Z0-9_]+$", ErrorMessage = "用户名只能包含字母、数字和下划线")]
private string userName = string.Empty;
```

---

## 🏷️ 5. 消息传递相关标签

### **消息发送和接收**
```csharp
// 发送消息的属性
[ObservableProperty]
[NotifyPropertyChangedRecipients]
private string currentUser = string.Empty;

// 接收消息的方法
[RelayCommand]
private void HandleUserChanged()
{
    // 处理用户变更消息
}
```

---

## 🏷️ 6. 依赖注入相关

### **IoC 容器使用**
```csharp
// 在 App.xaml.cs 中配置
Ioc.Default.ConfigureServices(services =>
{
    services.AddSingleton<IUserService, UserService>();
    services.AddTransient<MainViewModel>();
});

// 在 ViewModel 中使用
public class MainViewModel : ObservableObject
{
    private readonly IUserService _userService;
    
    public MainViewModel()
    {
        _userService = Ioc.Default.GetService<IUserService>();
    }
}
```

---

## 🎯 7. 完整示例：现代化 ViewModel

```csharp
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel.DataAnnotations;

namespace MyApp.ViewModels;

public partial class UserProfileViewModel : ObservableValidator
{
    #region 基础属性
    
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(FullName))]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = "姓名不能为空")]
    [MinLength(2, ErrorMessage = "姓名至少2个字符")]
    private string firstName = string.Empty;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(FullName))]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    [NotifyDataErrorInfo]
    [Required]
    private string lastName = string.Empty;

    [ObservableProperty]
    [NotifyDataErrorInfo]
    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    private string email = string.Empty;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(DeleteCommand))]
    private bool isSelected;

    [ObservableProperty]
    private bool isLoading;

    #endregion

    #region 计算属性

    public string FullName => $"{FirstName} {LastName}".Trim();

    #endregion

    #region 命令

    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task SaveAsync()
    {
        IsLoading = true;
        try
        {
            ValidateAllProperties();
            if (!HasErrors)
            {
                // 保存逻辑
                await Task.Delay(1000); // 模拟保存
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand(CanExecute = nameof(CanDelete))]
    private async Task DeleteAsync()
    {
        // 删除逻辑
        await Task.Delay(500);
    }

    [RelayCommand(IncludeCancelCommand = true)]
    private async Task LoadDataAsync(CancellationToken cancellationToken)
    {
        IsLoading = true;
        try
        {
            // 长时间加载操作
            await Task.Delay(5000, cancellationToken);
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void Reset()
    {
        FirstName = string.Empty;
        LastName = string.Empty;
        Email = string.Empty;
        ClearErrors();
    }

    #endregion

    #region 属性变化处理

    partial void OnFirstNameChanged(string value)
    {
        // 姓名变化时的额外逻辑
        OnPropertyChanged(nameof(FullName));
    }

    partial void OnLastNameChanged(string value)
    {
        // 姓名变化时的额外逻辑
        OnPropertyChanged(nameof(FullName));
    }

    #endregion

    #region 辅助方法

    private bool CanSave()
    {
        return !IsLoading &&
               !string.IsNullOrWhiteSpace(FirstName) &&
               !string.IsNullOrWhiteSpace(LastName) &&
               !HasErrors;
    }

    private bool CanDelete()
    {
        return IsSelected && !IsLoading;
    }

    #endregion
}
```

---

## 🚀 8. 最佳实践总结

### **性能优化**
- ✅ 使用 `[ObservableProperty]` 替代手动属性实现
- ✅ 合理使用 `[NotifyPropertyChangedFor]` 避免过度通知
- ✅ 异步命令使用 `CancellationToken` 支持取消

### **代码组织**
- ✅ 使用 `#region` 组织代码结构
- ✅ 继承合适的基类（ObservableObject/ObservableValidator/ObservableRecipient）
- ✅ 使用 `partial void OnXxxChanged` 处理属性变化

### **验证和错误处理**
- ✅ 使用 `[NotifyDataErrorInfo]` + 验证标签
- ✅ 异步命令配置合适的异常处理策略
- ✅ 使用 `ObservableValidator.ValidateAllProperties()` 验证所有属性

### **命令设计**
- ✅ 合理设置 `CanExecute` 条件
- ✅ 长时间操作使用 `IncludeCancelCommand = true`
- ✅ 并发场景考虑 `AllowConcurrentExecutions` 设置

---

## 📚 9. 属性变化处理的高级用法

### **partial void 方法签名**
```csharp
// 只传递新值
partial void OnNameChanged(string value);
partial void OnNameChanging(string value);

// 传递旧值和新值
partial void OnNameChanged(string oldValue, string newValue);
partial void OnNameChanging(string oldValue, string newValue);
```

### **实际应用示例**
```csharp
[ObservableProperty]
private string searchText = string.Empty;

// 搜索文本变化时自动触发搜索
partial void OnSearchTextChanged(string value)
{
    if (!string.IsNullOrWhiteSpace(value))
    {
        _ = SearchAsync(value);
    }
}

[ObservableProperty]
private bool isDarkMode;

// 主题变化时更新UI
partial void OnIsDarkModeChanged(bool value)
{
    ThemeManager.SetDarkMode(value);
}
```

---

## 🔧 10. 高级配置和技巧

### **Source Generator 配置**
```xml
<!-- 在 .csproj 中配置 -->
<PropertyGroup>
    <IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
</PropertyGroup>

<ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
</ItemGroup>
```

### **调试 Source Generator**
```csharp
// 查看生成的代码（在 obj/Debug/net6.0/generated 文件夹中）
// 或使用 Visual Studio 的 "Go to Definition" 功能
```

### **性能监控**
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(ExpensiveProperty))]
private string baseValue = string.Empty;

// 避免在计算属性中进行昂贵操作
public string ExpensiveProperty =>
    string.IsNullOrEmpty(BaseValue) ? "Empty" : ProcessExpensiveOperation(BaseValue);
```

---

## 🎯 11. 常见问题和解决方案

### **问题1：partial class 要求**
```csharp
// ❌ 错误：缺少 partial 关键字
public class MyViewModel : ObservableObject

// ✅ 正确：必须是 partial class
public partial class MyViewModel : ObservableObject
```

### **问题2：嵌套类型的 partial 要求**
```csharp
// ❌ 错误：嵌套类型也需要 partial
public partial class OuterClass
{
    public class InnerViewModel : ObservableObject // 缺少 partial
}

// ✅ 正确：所有嵌套类型都需要 partial
public partial class OuterClass
{
    public partial class InnerViewModel : ObservableObject
}
```

### **问题3：命名约定**
```csharp
// ✅ 支持的字段命名约定
[ObservableProperty] private string name;        // → Name
[ObservableProperty] private string _name;       // → Name
[ObservableProperty] private string m_name;      // → Name
[ObservableProperty] private string userName;    // → UserName
[ObservableProperty] private string _userName;   // → UserName
```

---

## 📖 12. 参考资源

### **官方文档**
- [CommunityToolkit.Mvvm 官方文档](https://learn.microsoft.com/en-us/dotnet/communitytoolkit/mvvm/)
- [Source Generators 概述](https://learn.microsoft.com/en-us/dotnet/csharp/roslyn-sdk/source-generators-overview)

### **示例项目**
- [官方示例应用](https://aka.ms/mvvmtoolkit/samples)
- [单元测试示例](https://github.com/CommunityToolkit/dotnet/tree/main/tests/CommunityToolkit.Mvvm.UnitTests)

### **社区资源**
- [GitHub 仓库](https://github.com/CommunityToolkit/dotnet)
- [NuGet 包](https://www.nuget.org/packages/CommunityToolkit.Mvvm/)

---

## 🎉 总结

CommunityToolkit.Mvvm 通过 Source Generators 技术，提供了现代化、高性能、类型安全的 MVVM 开发体验：

- **简洁性**：大幅减少样板代码
- **性能**：编译时生成，零运行时开销
- **类型安全**：编译时检查，避免运行时错误
- **灵活性**：可选择性使用各种功能
- **兼容性**：支持所有 .NET 平台和 UI 框架

**这就是现代 .NET MVVM 开发的最佳实践！** 🎯✨

---

*文档创建时间：2025年1月*
*基于 CommunityToolkit.Mvvm 8.2.2 版本*
