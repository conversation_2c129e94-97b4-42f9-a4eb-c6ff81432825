using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Zylo.WPF.Models.Navigation;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Controls.Navigation
{
    /// <summary>
    /// MenuBarControl - 菜单栏控件
    /// 提供现代化的应用程序菜单栏功能，支持多级菜单、图标、快捷键等
    /// </summary>
    public partial class MenuBarControl : UserControl
    {
        #region 日志记录器

        /// <summary>
        /// 日志记录器实例
        /// </summary>
        private readonly YLoggerInstance _logger = YLogger.ForSilent<MenuBarControl>();

        #endregion

        #region 依赖属性

        /// <summary>
        /// 菜单项集合
        /// </summary>
        public static readonly DependencyProperty MenuItemsProperty =
            DependencyProperty.Register(nameof(MenuItems), typeof(ObservableCollection<MenuItemModel>), 
                typeof(MenuBarControl), new PropertyMetadata(new ObservableCollection<MenuItemModel>()));

        /// <summary>
        /// 菜单项集合
        /// </summary>
        public ObservableCollection<MenuItemModel> MenuItems
        {
            get => (ObservableCollection<MenuItemModel>)GetValue(MenuItemsProperty);
            set => SetValue(MenuItemsProperty, value);
        }

        /// <summary>
        /// 菜单项点击命令
        /// </summary>
        public static readonly DependencyProperty MenuItemClickCommandProperty =
            DependencyProperty.Register(nameof(MenuItemClickCommand), typeof(ICommand), 
                typeof(MenuBarControl), new PropertyMetadata(null));

        /// <summary>
        /// 菜单项点击命令
        /// </summary>
        public ICommand MenuItemClickCommand
        {
            get => (ICommand)GetValue(MenuItemClickCommandProperty);
            set => SetValue(MenuItemClickCommandProperty, value);
        }

        /// <summary>
        /// 是否启用菜单栏
        /// </summary>
        public static readonly DependencyProperty IsMenuEnabledProperty =
            DependencyProperty.Register(nameof(IsMenuEnabled), typeof(bool), 
                typeof(MenuBarControl), new PropertyMetadata(true));

        /// <summary>
        /// 是否启用菜单栏
        /// </summary>
        public bool IsMenuEnabled
        {
            get => (bool)GetValue(IsMenuEnabledProperty);
            set => SetValue(IsMenuEnabledProperty, value);
        }

        /// <summary>
        /// 菜单栏高度
        /// </summary>
        public static readonly DependencyProperty MenuHeightProperty =
            DependencyProperty.Register(nameof(MenuHeight), typeof(double), 
                typeof(MenuBarControl), new PropertyMetadata(40.0));

        /// <summary>
        /// 菜单栏高度
        /// </summary>
        public double MenuHeight
        {
            get => (double)GetValue(MenuHeightProperty);
            set => SetValue(MenuHeightProperty, value);
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 MenuBarControl
        /// </summary>
        public MenuBarControl()
        {
            InitializeComponent();

            // 不设置 DataContext，让外部控制
            // 初始化菜单项
            InitializeDefaultMenuItems();

            _logger.Info("✅ MenuBarControl 初始化完成");
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化默认菜单项
        /// </summary>
        private void InitializeDefaultMenuItems()
        {
            try
            {
                if (MenuItems == null)
                {
                    MenuItems = new ObservableCollection<MenuItemModel>();
                }

                // 如果没有菜单项，创建默认菜单
                if (MenuItems.Count == 0)
                {
                    CreateDefaultMenuStructure();
                }

                _logger.Info($"✅ 默认菜单项初始化完成，共 {MenuItems.Count} 个顶级菜单");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 初始化默认菜单项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建默认菜单结构
        /// </summary>
        private void CreateDefaultMenuStructure()
        {
            // 文件菜单
            var fileMenu = new MenuItemModel
            {
                Header = "文件(_F)",
                Children = new ObservableCollection<MenuItemModel>
                {
                    new MenuItemModel { Header = "新建(_N)", InputGestureText = "Ctrl+N" },
                    new MenuItemModel { Header = "打开(_O)", InputGestureText = "Ctrl+O" },
                    new MenuItemModel { Header = "保存(_S)", InputGestureText = "Ctrl+S" },
                    new MenuItemModel { Header = "另存为(_A)", InputGestureText = "Ctrl+Shift+S" },
                    new MenuItemModel { IsSeparator = true },
                    new MenuItemModel { Header = "退出(_X)", InputGestureText = "Alt+F4" }
                }
            };

            // 编辑菜单
            var editMenu = new MenuItemModel
            {
                Header = "编辑(_E)",
                Children = new ObservableCollection<MenuItemModel>
                {
                    new MenuItemModel { Header = "撤销(_U)", InputGestureText = "Ctrl+Z" },
                    new MenuItemModel { Header = "重做(_R)", InputGestureText = "Ctrl+Y" },
                    new MenuItemModel { IsSeparator = true },
                    new MenuItemModel { Header = "剪切(_T)", InputGestureText = "Ctrl+X" },
                    new MenuItemModel { Header = "复制(_C)", InputGestureText = "Ctrl+C" },
                    new MenuItemModel { Header = "粘贴(_P)", InputGestureText = "Ctrl+V" },
                    new MenuItemModel { IsSeparator = true },
                    new MenuItemModel { Header = "全选(_A)", InputGestureText = "Ctrl+A" }
                }
            };

            // 视图菜单
            var viewMenu = new MenuItemModel
            {
                Header = "视图(_V)",
                Children = new ObservableCollection<MenuItemModel>
                {
                    new MenuItemModel { Header = "工具栏(_T)" },
                    new MenuItemModel { Header = "状态栏(_S)" },
                    new MenuItemModel { IsSeparator = true },
                    new MenuItemModel { Header = "全屏(_F)", InputGestureText = "F11" }
                }
            };

            // 工具菜单
            var toolsMenu = new MenuItemModel
            {
                Header = "工具(_T)",
                Children = new ObservableCollection<MenuItemModel>
                {
                    new MenuItemModel { Header = "选项(_O)" },
                    new MenuItemModel { Header = "自定义(_C)" }
                }
            };

            // 帮助菜单
            var helpMenu = new MenuItemModel
            {
                Header = "帮助(_H)",
                Children = new ObservableCollection<MenuItemModel>
                {
                    new MenuItemModel { Header = "帮助主题(_H)", InputGestureText = "F1" },
                    new MenuItemModel { IsSeparator = true },
                    new MenuItemModel { Header = "关于(_A)" }
                }
            };

            // 添加到菜单集合
            MenuItems.Add(fileMenu);
            MenuItems.Add(editMenu);
            MenuItems.Add(viewMenu);
            MenuItems.Add(toolsMenu);
            MenuItems.Add(helpMenu);
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 控件加载完成事件
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 设置菜单栏高度
                Height = MenuHeight;
                
                // 绑定菜单项点击事件
                if (MainMenuBar != null)
                {
                    MainMenuBar.IsEnabled = IsMenuEnabled;
                }

                _logger.Info("✅ MenuBarControl 加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ MenuBarControl 加载失败: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加菜单项
        /// </summary>
        /// <param name="menuItem">要添加的菜单项</param>
        public void AddMenuItem(MenuItemModel menuItem)
        {
            try
            {
                if (menuItem != null)
                {
                    MenuItems.Add(menuItem);
                    _logger.Info($"✅ 添加菜单项: {menuItem.Header}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 添加菜单项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除菜单项
        /// </summary>
        /// <param name="menuItem">要移除的菜单项</param>
        public void RemoveMenuItem(MenuItemModel menuItem)
        {
            try
            {
                if (menuItem != null && MenuItems.Contains(menuItem))
                {
                    MenuItems.Remove(menuItem);
                    _logger.Info($"✅ 移除菜单项: {menuItem.Header}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 移除菜单项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空所有菜单项
        /// </summary>
        public void ClearMenuItems()
        {
            try
            {
                MenuItems.Clear();
                _logger.Info("✅ 清空所有菜单项");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 清空菜单项失败: {ex.Message}");
            }
        }

        #endregion
    }
}
