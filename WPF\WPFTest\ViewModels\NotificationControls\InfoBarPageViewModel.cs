using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;
using Timer = System.Threading.Timer;

namespace WPFTest.ViewModels.NotificationControls;

/// <summary>
/// InfoBar 页面 ViewModel - 使用 CommunityToolkit.Mvvm 现代化 MVVM 模式
/// 展示 InfoBar 控件的各种功能和用法
/// </summary>
public partial class InfoBarPageViewModel : ObservableObject
{
    #region 私有字段

    private readonly YLoggerInstance _logger = YLogger.ForDebug<InfoBarPageViewModel>();
    private CancellationTokenSource? _syncCancellationTokenSource;

    // 自动关闭定时器字典
    private readonly Dictionary<string, Timer> _autoCloseTimers = new();

    #endregion

    #region 基础 InfoBar 状态属性

    [ObservableProperty]
    private bool isInfoBarOpen = true;

    [ObservableProperty]
    private bool isSuccessBarOpen = true;

    [ObservableProperty]
    private bool isWarningBarOpen = true;

    [ObservableProperty]
    private bool isErrorBarOpen = true;

    #endregion

    #region 高级 InfoBar 状态属性

    [ObservableProperty]
    private bool isUpdateBarOpen = false;

    [ObservableProperty]
    private bool isSyncBarOpen = false;

    [ObservableProperty]
    private double syncProgress = 0;

    [ObservableProperty]
    private string syncProgressText = "准备同步...";

    #endregion

    #region 动态 InfoBar 属性

    [ObservableProperty]
    private bool isDynamicBarOpen = false;

    [ObservableProperty]
    private string dynamicTitle = "动态标题";

    [ObservableProperty]
    private string dynamicMessage = "动态消息内容";

    [ObservableProperty]
    private InfoBarSeverity dynamicSeverity = InfoBarSeverity.Informational;

    [ObservableProperty]
    private string currentStatus = "就绪";

    [ObservableProperty]
    private DateTime lastActionTime = DateTime.Now;

    #endregion

    #region 自动关闭功能属性

    [ObservableProperty]
    private bool enableAutoClose = true;

    [ObservableProperty]
    private int autoCloseSeconds = 5;

    [ObservableProperty]
    private string autoCloseCountdown = string.Empty;

    /// <summary>
    /// 当延迟时间改变时触发
    /// </summary>
    partial void OnAutoCloseSecondsChanged(int value)
    {
        _logger.Info($"🔧 延迟时间已更改为: {value} 秒");
        UpdateStatus($"延迟时间设置为 {value} 秒");
    }

    #endregion

    #region 代码示例属性

    [ObservableProperty]
    private string basicXamlExample = string.Empty;

    [ObservableProperty]
    private string basicCSharpExample = string.Empty;

    [ObservableProperty]
    private string advancedXamlExample = string.Empty;

    [ObservableProperty]
    private string advancedCSharpExample = string.Empty;

    [ObservableProperty]
    private string mvvmXamlExample = string.Empty;

    [ObservableProperty]
    private string mvvmCSharpExample = string.Empty;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public InfoBarPageViewModel()
    {
        try
        {
            _logger.Info("🚀 InfoBarPageViewModel 构造函数开始");
            
            // 初始化代码示例
            InitializeCodeExamples();
            
            _logger.Info("✅ InfoBarPageViewModel 构造完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ InfoBarPageViewModel 构造失败: {ex}");
        }
    }

    #endregion

    #region 基础控制命令

    [RelayCommand]
    private void ShowInfoBar()
    {
        IsInfoBarOpen = true;
        UpdateStatus("显示信息栏");
        _logger.Info("📢 显示信息类型 InfoBar");

        // 启动自动关闭
        if (EnableAutoClose)
        {
            StartAutoClose("InfoBar", () => IsInfoBarOpen = false);
        }
    }

    [RelayCommand]
    private void ShowSuccessBar()
    {
        IsSuccessBarOpen = true;
        UpdateStatus("显示成功栏");
        _logger.Info("✅ 显示成功类型 InfoBar");

        // 启动自动关闭
        if (EnableAutoClose)
        {
            StartAutoClose("SuccessBar", () => IsSuccessBarOpen = false);
        }
    }

    [RelayCommand]
    private void ShowWarningBar()
    {
        IsWarningBarOpen = true;
        UpdateStatus("显示警告栏");
        _logger.Info("⚠️ 显示警告类型 InfoBar");

        // 启动自动关闭
        if (EnableAutoClose)
        {
            StartAutoClose("WarningBar", () => IsWarningBarOpen = false);
        }
    }

    [RelayCommand]
    private void ShowErrorBar()
    {
        IsErrorBarOpen = true;
        UpdateStatus("显示错误栏");
        _logger.Info("❌ 显示错误类型 InfoBar");

        // 启动自动关闭
        if (EnableAutoClose)
        {
            StartAutoClose("ErrorBar", () => IsErrorBarOpen = false);
        }
    }

    [RelayCommand]
    private void HideAllBars()
    {
        // 停止所有自动关闭定时器
        StopAllAutoCloseTimers();

        IsInfoBarOpen = false;
        IsSuccessBarOpen = false;
        IsWarningBarOpen = false;
        IsErrorBarOpen = false;
        IsUpdateBarOpen = false;
        IsSyncBarOpen = false;
        IsDynamicBarOpen = false;
        UpdateStatus("隐藏所有信息栏");
        _logger.Info("🙈 隐藏所有 InfoBar");
    }

    #endregion

    #region 高级功能命令

    [RelayCommand]
    private void ShowUpdateBar()
    {
        IsUpdateBarOpen = true;
        UpdateStatus("显示更新提示");
        _logger.Info("🔄 显示更新提示 InfoBar");
    }

    [RelayCommand]
    private void Update()
    {
        UpdateStatus("开始更新...");
        _logger.Info("🚀 执行更新操作");
        
        // 模拟更新操作
        Task.Run(async () =>
        {
            await Task.Delay(2000);
            await App.Current.Dispatcher.InvokeAsync(() =>
            {
                IsUpdateBarOpen = false;
                ShowDynamicBar("更新完成", "应用程序已成功更新到最新版本", InfoBarSeverity.Success);
            });
        });
    }

    [RelayCommand]
    private async Task StartSyncAsync()
    {
        try
        {
            _syncCancellationTokenSource?.Cancel();
            _syncCancellationTokenSource = new CancellationTokenSource();
            
            IsSyncBarOpen = true;
            SyncProgress = 0;
            SyncProgressText = "开始同步...";
            UpdateStatus("同步进行中");
            
            _logger.Info("🔄 开始文件同步");
            
            // 模拟同步进度
            for (int i = 0; i <= 100; i += 5)
            {
                if (_syncCancellationTokenSource.Token.IsCancellationRequested)
                    break;
                    
                SyncProgress = i;
                SyncProgressText = $"正在同步... {i}%";
                await Task.Delay(200, _syncCancellationTokenSource.Token);
            }
            
            if (!_syncCancellationTokenSource.Token.IsCancellationRequested)
            {
                SyncProgressText = "同步完成";
                await Task.Delay(1000);
                IsSyncBarOpen = false;
                ShowDynamicBar("同步成功", "所有文件已成功同步到云端", InfoBarSeverity.Success);
            }
        }
        catch (OperationCanceledException)
        {
            SyncProgressText = "同步已取消";
            UpdateStatus("同步已停止");
            _logger.Info("⏹️ 文件同步已取消");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 同步过程出错: {ex}");
            ShowDynamicBar("同步失败", $"同步过程中发生错误: {ex.Message}", InfoBarSeverity.Error);
        }
    }

    [RelayCommand]
    private void StopSync()
    {
        _syncCancellationTokenSource?.Cancel();
        IsSyncBarOpen = false;
        UpdateStatus("同步已停止");
        _logger.Info("⏹️ 停止文件同步");
    }

    #endregion

    #region MVVM 示例命令

    [RelayCommand]
    private void SimulateSuccess()
    {
        ShowDynamicBar("操作成功", "数据保存成功，所有更改已应用", InfoBarSeverity.Success);
        UpdateStatus("模拟成功操作完成");
        _logger.Info("✅ 模拟成功操作");
    }

    [RelayCommand]
    private void SimulateError()
    {
        ShowDynamicBar("操作失败", "网络连接超时，请检查网络设置后重试", InfoBarSeverity.Error);
        UpdateStatus("模拟错误操作完成");
        _logger.Info("❌ 模拟错误操作");
    }

    [RelayCommand]
    private async Task SimulateAsyncAsync()
    {
        try
        {
            ShowDynamicBar("处理中", "正在执行异步操作，请稍候...", InfoBarSeverity.Informational);
            UpdateStatus("异步操作进行中");
            
            // 模拟异步操作
            await Task.Delay(3000);
            
            ShowDynamicBar("处理完成", "异步操作已成功完成", InfoBarSeverity.Success);
            UpdateStatus("异步操作完成");
            _logger.Info("🔄 异步操作完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 异步操作失败: {ex}");
            ShowDynamicBar("处理失败", $"异步操作失败: {ex.Message}", InfoBarSeverity.Error);
        }
    }

    [RelayCommand]
    private void ClearStatus()
    {
        IsDynamicBarOpen = false;
        CurrentStatus = "就绪";
        UpdateStatus("状态已清除");
        _logger.Info("🧹 清除状态");
    }

    [RelayCommand]
    private void TestDelay()
    {
        var message = $"当前延迟时间设置: {AutoCloseSeconds} 秒";
        ShowDynamicBar("延迟测试", message, InfoBarSeverity.Informational);
        UpdateStatus($"测试延迟时间: {AutoCloseSeconds} 秒");
        _logger.Info($"🧪 测试延迟时间: {AutoCloseSeconds} 秒");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 显示动态 InfoBar
    /// </summary>
    private void ShowDynamicBar(string title, string message, InfoBarSeverity severity)
    {
        DynamicTitle = title;
        DynamicMessage = message;
        DynamicSeverity = severity;
        IsDynamicBarOpen = true;

        // 启动自动关闭（除了正在处理的状态）
        if (EnableAutoClose && severity != InfoBarSeverity.Informational)
        {
            StartAutoClose("DynamicBar", () => IsDynamicBarOpen = false);
        }
    }

    /// <summary>
    /// 更新状态
    /// </summary>
    private void UpdateStatus(string status)
    {
        CurrentStatus = status;
        LastActionTime = DateTime.Now;
    }

    /// <summary>
    /// 启动自动关闭定时器
    /// </summary>
    /// <param name="timerKey">定时器唯一标识</param>
    /// <param name="closeAction">关闭动作</param>
    private void StartAutoClose(string timerKey, Action closeAction)
    {
        // 停止现有定时器
        StopAutoCloseTimer(timerKey);

        // 使用当前设置的延迟时间
        var totalSeconds = AutoCloseSeconds;
        var remainingSeconds = totalSeconds;

        _logger.Info($"⏰ 启动 {timerKey} 自动关闭定时器，使用延迟时间: {totalSeconds} 秒");

        // 创建新的定时器
        var timer = new Timer(_ =>
        {
            remainingSeconds--;

            // 在 UI 线程上更新倒计时显示
            App.Current.Dispatcher.Invoke(() =>
            {
                if (remainingSeconds > 0)
                {
                    AutoCloseCountdown = $"将在 {remainingSeconds} 秒后自动关闭";
                }
                else
                {
                    AutoCloseCountdown = string.Empty;
                    closeAction?.Invoke();
                    StopAutoCloseTimer(timerKey);
                    _logger.Info($"⏰ {timerKey} 自动关闭完成");
                }
            });

        }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        _autoCloseTimers[timerKey] = timer;
        AutoCloseCountdown = $"将在 {remainingSeconds} 秒后自动关闭";
    }

    /// <summary>
    /// 停止自动关闭定时器
    /// </summary>
    /// <param name="timerKey">定时器唯一标识</param>
    private void StopAutoCloseTimer(string timerKey)
    {
        if (_autoCloseTimers.TryGetValue(timerKey, out var timer))
        {
            timer?.Dispose();
            _autoCloseTimers.Remove(timerKey);
            AutoCloseCountdown = string.Empty;
        }
    }

    /// <summary>
    /// 停止所有自动关闭定时器
    /// </summary>
    private void StopAllAutoCloseTimers()
    {
        foreach (var timer in _autoCloseTimers.Values)
        {
            timer?.Dispose();
        }
        _autoCloseTimers.Clear();
        AutoCloseCountdown = string.Empty;
    }

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamples()
    {
        // 基础示例
        BasicXamlExample = @"<!-- InfoBar 基础用法 -->
<ui:InfoBar Title=""信息提示""
            Message=""这是一个信息类型的 InfoBar""
            Severity=""Informational""
            IsOpen=""{Binding IsInfoBarOpen}""
            IsClosable=""True""/>

<ui:InfoBar Title=""操作成功""
            Message=""文件已成功保存""
            Severity=""Success""
            IsOpen=""{Binding IsSuccessBarOpen}""
            IsClosable=""True""/>

<ui:InfoBar Title=""警告提示""
            Message=""磁盘空间不足""
            Severity=""Warning""
            IsOpen=""{Binding IsWarningBarOpen}""
            IsClosable=""True""/>

<ui:InfoBar Title=""错误信息""
            Message=""网络连接失败""
            Severity=""Error""
            IsOpen=""{Binding IsErrorBarOpen}""
            IsClosable=""True""/>";

        BasicCSharpExample = @"// InfoBar 基础控制 - 使用 CommunityToolkit.Mvvm
public partial class InfoBarPageViewModel : ObservableObject
{
    [ObservableProperty]
    private bool isInfoBarOpen = true;

    [ObservableProperty]
    private bool enableAutoClose = true;

    [ObservableProperty]
    private int autoCloseSeconds = 5;

    [ObservableProperty]
    private string autoCloseCountdown = string.Empty;

    // 自动关闭定时器字典
    private readonly Dictionary<string, Timer> _autoCloseTimers = new();

    [RelayCommand]
    private void ShowInfoBar()
    {
        IsInfoBarOpen = true;

        // 启动自动关闭
        if (EnableAutoClose)
        {
            StartAutoClose(""InfoBar"", () => IsInfoBarOpen = false);
        }
    }

    /// <summary>
    /// 启动自动关闭定时器
    /// </summary>
    private void StartAutoClose(string timerKey, Action closeAction)
    {
        StopAutoCloseTimer(timerKey);
        var remainingSeconds = AutoCloseSeconds;

        var timer = new Timer(_ =>
        {
            remainingSeconds--;
            App.Current.Dispatcher.Invoke(() =>
            {
                if (remainingSeconds > 0)
                {
                    AutoCloseCountdown = $""将在 {remainingSeconds} 秒后自动关闭"";
                }
                else
                {
                    AutoCloseCountdown = string.Empty;
                    closeAction?.Invoke();
                    StopAutoCloseTimer(timerKey);
                }
            });
        }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        _autoCloseTimers[timerKey] = timer;
        AutoCloseCountdown = $""将在 {remainingSeconds} 秒后自动关闭"";
    }
}";

        // 高级示例
        AdvancedXamlExample = @"<!-- 带动作按钮的 InfoBar -->
<StackPanel>
    <ui:InfoBar Title=""更新可用""
                Message=""发现新版本，包含重要更新""
                Severity=""Informational""
                IsOpen=""{Binding IsUpdateBarOpen}""
                IsClosable=""True""/>
    <ui:Button Content=""立即更新""
               Icon=""{ui:SymbolIcon ArrowDownload24}""
               Command=""{Binding UpdateCommand}""
               Appearance=""Primary""
               HorizontalAlignment=""Right""
               Margin=""0,8,0,0""/>
</StackPanel>

<!-- 带自定义内容的 InfoBar -->
<StackPanel>
    <ui:InfoBar Title=""文件同步""
                Message=""正在同步文件到云端...""
                Severity=""Warning""
                IsOpen=""{Binding IsSyncBarOpen}""
                IsClosable=""True""/>
    <ui:Card Padding=""16"" Margin=""0,8,0,0"">
        <StackPanel>
            <ProgressBar Value=""{Binding SyncProgress}"" Maximum=""100""/>
            <TextBlock Text=""{Binding SyncProgressText}""/>
        </StackPanel>
    </ui:Card>
</StackPanel>";

        AdvancedCSharpExample = @"// InfoBar 高级功能控制
public partial class InfoBarPageViewModel : ObservableObject
{
    [ObservableProperty]
    private bool isUpdateBarOpen = false;

    [ObservableProperty]
    private double syncProgress = 0;

    [ObservableProperty]
    private string syncProgressText = ""准备同步..."";

    [RelayCommand]
    private void Update()
    {
        // 执行更新操作
        Task.Run(async () =>
        {
            await Task.Delay(2000);
            // 更新完成后隐藏 InfoBar
            IsUpdateBarOpen = false;
        });
    }

    [RelayCommand]
    private async Task StartSyncAsync()
    {
        IsSyncBarOpen = true;
        for (int i = 0; i <= 100; i += 5)
        {
            SyncProgress = i;
            SyncProgressText = $""正在同步... {i}%"";
            await Task.Delay(200);
        }
    }
}";

        // MVVM 示例
        MvvmXamlExample = @"<!-- 动态 InfoBar 绑定 -->
<ui:InfoBar Title=""{Binding DynamicTitle}""
            Message=""{Binding DynamicMessage}""
            Severity=""{Binding DynamicSeverity}""
            IsOpen=""{Binding IsDynamicBarOpen}""
            IsClosable=""True""/>

<!-- 状态显示 -->
<TextBlock Text=""{Binding CurrentStatus}""/>
<TextBlock Text=""{Binding LastActionTime, 
                  StringFormat='最后操作: {0:HH:mm:ss}'}""/>";

        MvvmCSharpExample = @"// 现代化 MVVM 模式 - CommunityToolkit.Mvvm
public partial class InfoBarPageViewModel : ObservableObject
{
    [ObservableProperty]
    private bool isDynamicBarOpen = false;

    [ObservableProperty]
    private string dynamicTitle = ""动态标题"";

    [ObservableProperty]
    private string dynamicMessage = ""动态消息"";

    [ObservableProperty]
    private InfoBarSeverity dynamicSeverity = InfoBarSeverity.Informational;

    [ObservableProperty]
    private string currentStatus = ""就绪"";

    [ObservableProperty]
    private DateTime lastActionTime = DateTime.Now;

    [RelayCommand]
    private void SimulateSuccess()
    {
        ShowDynamicBar(""操作成功"", ""数据保存成功"", InfoBarSeverity.Success);
        UpdateStatus(""操作完成"");
    }

    [RelayCommand]
    private async Task SimulateAsyncAsync()
    {
        ShowDynamicBar(""处理中"", ""正在执行操作..."", InfoBarSeverity.Informational);
        await Task.Delay(3000);
        ShowDynamicBar(""完成"", ""操作成功完成"", InfoBarSeverity.Success);
    }

    private void ShowDynamicBar(string title, string message, InfoBarSeverity severity)
    {
        DynamicTitle = title;
        DynamicMessage = message;
        DynamicSeverity = severity;
        IsDynamicBarOpen = true;
    }

    private void UpdateStatus(string status)
    {
        CurrentStatus = status;
        LastActionTime = DateTime.Now;
    }
}";

        _logger.Info("📝 代码示例初始化完成");
    }

    #endregion
}
