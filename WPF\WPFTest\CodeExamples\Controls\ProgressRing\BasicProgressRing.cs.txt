using System.Windows.Controls;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Examples
{
    /// <summary>
    /// 基础 ProgressRing 示例 - 演示基本用法
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class BasicProgressRingExample : UserControl
    {
        public BasicProgressRingExample()
        {
            InitializeComponent();
            DataContext = new BasicProgressRingViewModel();
        }
    }

    /// <summary>
    /// 基础 ProgressRing ViewModel
    /// </summary>
    public partial class BasicProgressRingViewModel : ObservableObject
    {
        private readonly DispatcherTimer _timer;

        #region 属性 - 使用 Partial Properties 语法

        /// <summary>
        /// 是否为不确定进度模式
        /// </summary>
        [ObservableProperty]
        public partial bool IsIndeterminate { get; set; } = true;

        /// <summary>
        /// 当前进度值
        /// </summary>
        [ObservableProperty]
        public partial double ProgressValue { get; set; } = 0;

        /// <summary>
        /// 最大进度值
        /// </summary>
        [ObservableProperty]
        public partial double MaximumValue { get; set; } = 100;

        /// <summary>
        /// 进度环大小
        /// </summary>
        [ObservableProperty]
        public partial double RingSize { get; set; } = 48;

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "准备就绪";

        #endregion

        #region 构造函数

        public BasicProgressRingViewModel()
        {
            // 初始化定时器用于进度演示
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _timer.Tick += Timer_Tick;
        }

        #endregion

        #region 命令

        /// <summary>
        /// 开始进度命令
        /// </summary>
        [RelayCommand]
        private void StartProgress()
        {
            IsIndeterminate = false;
            ProgressValue = 0;
            StatusMessage = "进度开始";
            _timer.Start();
        }

        /// <summary>
        /// 停止进度命令
        /// </summary>
        [RelayCommand]
        private void StopProgress()
        {
            _timer.Stop();
            StatusMessage = "进度停止";
        }

        /// <summary>
        /// 重置进度命令
        /// </summary>
        [RelayCommand]
        private void ResetProgress()
        {
            _timer.Stop();
            ProgressValue = 0;
            IsIndeterminate = true;
            StatusMessage = "进度重置";
        }

        /// <summary>
        /// 切换模式命令
        /// </summary>
        [RelayCommand]
        private void ToggleMode()
        {
            IsIndeterminate = !IsIndeterminate;
            StatusMessage = IsIndeterminate ? "切换到不确定模式" : "切换到确定模式";
            
            if (IsIndeterminate)
            {
                _timer.Stop();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 定时器事件处理
        /// </summary>
        private void Timer_Tick(object? sender, EventArgs e)
        {
            if (ProgressValue >= MaximumValue)
            {
                _timer.Stop();
                StatusMessage = "进度完成！";
                UpdateProgressText();
                return;
            }

            ProgressValue += 2;
            StatusMessage = $"进度: {ProgressValue:F0}%";
            UpdateProgressText();
        }

        /// <summary>
        /// 更新进度文本显示
        /// </summary>
        private void UpdateProgressText()
        {
            // 注意：WPF-UI 的 ProgressRing 不支持 Value 属性
            // 这里只是为了演示进度文本的更新逻辑
            if (IsIndeterminate)
            {
                StatusMessage = "加载中...";
            }
            else
            {
                StatusMessage = $"进度: {ProgressValue:F0}%";
            }
        }

        #endregion
    }

    /// <summary>
    /// 基础 ProgressRing 使用示例
    /// </summary>
    public static class ProgressRingBasicUsage
    {
        /// <summary>
        /// 创建不确定进度环
        /// </summary>
        public static ProgressRing CreateIndeterminateRing()
        {
            return new ProgressRing
            {
                IsIndeterminate = true,
                Width = 48,
                Height = 48
            };
        }

        /// <summary>
        /// 创建确定进度环（静态模式）
        /// </summary>
        public static ProgressRing CreateDeterminateRing()
        {
            return new ProgressRing
            {
                IsIndeterminate = false,
                Width = 48,
                Height = 48
            };
        }

        /// <summary>
        /// 创建自定义大小进度环
        /// </summary>
        public static ProgressRing CreateCustomSizeRing(double size, bool isIndeterminate = true)
        {
            return new ProgressRing
            {
                IsIndeterminate = isIndeterminate,
                Width = size,
                Height = size
            };
        }

        /// <summary>
        /// 设置进度值
        /// </summary>
        public static void SetProgress(ProgressRing ring, double value)
        {
            if (ring != null)
            {
                ring.IsIndeterminate = false;
                ring.Value = Math.Max(0, Math.Min(ring.Maximum, value));
            }
        }

        /// <summary>
        /// 启用不确定模式
        /// </summary>
        public static void EnableIndeterminateMode(ProgressRing ring)
        {
            if (ring != null)
            {
                ring.IsIndeterminate = true;
            }
        }
    }
}
