using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace Zylo.WPF.Models.Navigation
{
    /// <summary>
    /// 菜单项模型
    /// 用于 MenuBarControl 的数据绑定
    /// </summary>
    public partial class MenuItemModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 菜单项标题
        /// </summary>
        [ObservableProperty]
        public partial string Header { get; set; } = string.Empty;

        /// <summary>
        /// 菜单项图标
        /// </summary>
        [ObservableProperty]
        public partial object? Icon { get; set; }

        /// <summary>
        /// 菜单项命令
        /// </summary>
        [ObservableProperty]
        public partial ICommand? Command { get; set; }

        /// <summary>
        /// 命令参数
        /// </summary>
        [ObservableProperty]
        public partial object? CommandParameter { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [ObservableProperty]
        public partial bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否可见
        /// </summary>
        [ObservableProperty]
        public partial bool IsVisible { get; set; } = true;

        /// <summary>
        /// 是否选中（用于复选框菜单项）
        /// </summary>
        [ObservableProperty]
        public partial bool IsChecked { get; set; }

        /// <summary>
        /// 是否为分隔符
        /// </summary>
        [ObservableProperty]
        public partial bool IsSeparator { get; set; }

        /// <summary>
        /// 快捷键文本
        /// </summary>
        [ObservableProperty]
        public partial string InputGestureText { get; set; } = string.Empty;

        /// <summary>
        /// 工具提示
        /// </summary>
        [ObservableProperty]
        public partial string ToolTip { get; set; } = string.Empty;

        /// <summary>
        /// 菜单项标识符
        /// </summary>
        [ObservableProperty]
        public partial string Id { get; set; } = string.Empty;

        /// <summary>
        /// 菜单项类型
        /// </summary>
        [ObservableProperty]
        public partial MenuItemType ItemType { get; set; } = MenuItemType.Normal;

        /// <summary>
        /// 子菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel>? Children { get; set; }

        /// <summary>
        /// 父菜单项
        /// </summary>
        [ObservableProperty]
        public partial MenuItemModel? Parent { get; set; }

        /// <summary>
        /// 菜单项数据
        /// </summary>
        [ObservableProperty]
        public partial object? Tag { get; set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MenuItemModel()
        {
            Children = new ObservableCollection<MenuItemModel>();
        }

        /// <summary>
        /// 带标题的构造函数
        /// </summary>
        /// <param name="header">菜单项标题</param>
        public MenuItemModel(string header) : this()
        {
            Header = header;
        }

        /// <summary>
        /// 带标题和命令的构造函数
        /// </summary>
        /// <param name="header">菜单项标题</param>
        /// <param name="command">菜单项命令</param>
        public MenuItemModel(string header, ICommand command) : this(header)
        {
            Command = command;
        }

        /// <summary>
        /// 带标题、命令和参数的构造函数
        /// </summary>
        /// <param name="header">菜单项标题</param>
        /// <param name="command">菜单项命令</param>
        /// <param name="commandParameter">命令参数</param>
        public MenuItemModel(string header, ICommand command, object commandParameter) : this(header, command)
        {
            CommandParameter = commandParameter;
        }

        #endregion

        #region 方法

        /// <summary>
        /// 添加子菜单项
        /// </summary>
        /// <param name="child">子菜单项</param>
        public void AddChild(MenuItemModel child)
        {
            if (child != null)
            {
                Children ??= new ObservableCollection<MenuItemModel>();
                child.Parent = this;
                Children.Add(child);
            }
        }

        /// <summary>
        /// 移除子菜单项
        /// </summary>
        /// <param name="child">子菜单项</param>
        public void RemoveChild(MenuItemModel child)
        {
            if (child != null && Children != null && Children.Contains(child))
            {
                child.Parent = null;
                Children.Remove(child);
            }
        }

        /// <summary>
        /// 清空所有子菜单项
        /// </summary>
        public void ClearChildren()
        {
            if (Children != null)
            {
                foreach (var child in Children)
                {
                    child.Parent = null;
                }
                Children.Clear();
            }
        }

        /// <summary>
        /// 获取菜单项的完整路径
        /// </summary>
        /// <returns>菜单项路径</returns>
        public string GetFullPath()
        {
            var path = Header;
            var current = Parent;
            
            while (current != null)
            {
                path = $"{current.Header} > {path}";
                current = current.Parent;
            }
            
            return path;
        }

        /// <summary>
        /// 查找子菜单项
        /// </summary>
        /// <param name="id">菜单项ID</param>
        /// <returns>找到的菜单项，如果没找到返回null</returns>
        public MenuItemModel? FindChild(string id)
        {
            if (Children == null) return null;

            foreach (var child in Children)
            {
                if (child.Id == id) return child;
                
                var found = child.FindChild(id);
                if (found != null) return found;
            }

            return null;
        }

        /// <summary>
        /// 检查是否有子菜单项
        /// </summary>
        /// <returns>如果有子菜单项返回true，否则返回false</returns>
        public bool HasChildren => Children != null && Children.Count > 0;

        #endregion

        #region 重写方法

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>菜单项的字符串表示</returns>
        public override string ToString()
        {
            return $"MenuItemModel: {Header} (ID: {Id}, Children: {Children?.Count ?? 0})";
        }

        #endregion
    }

    /// <summary>
    /// 菜单项类型枚举
    /// </summary>
    public enum MenuItemType
    {
        /// <summary>
        /// 普通菜单项
        /// </summary>
        Normal,

        /// <summary>
        /// 复选框菜单项
        /// </summary>
        CheckBox,

        /// <summary>
        /// 单选按钮菜单项
        /// </summary>
        RadioButton,

        /// <summary>
        /// 分隔符
        /// </summary>
        Separator,

        /// <summary>
        /// 子菜单
        /// </summary>
        SubMenu
    }
}
