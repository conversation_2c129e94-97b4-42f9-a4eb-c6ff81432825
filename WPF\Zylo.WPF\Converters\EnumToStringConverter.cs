using System.Globalization;
using System.Windows.Data;

namespace Zylo.WPF.Converters;

/// <summary>
/// 枚举到字符串转换器
/// </summary>
/// <remarks>
/// 功能：将枚举值转换为字符串，支持双向绑定
/// - 正向转换：枚举值 → 字符串
/// - 反向转换：字符串 → 枚举值
/// 
/// 使用场景：ComboBox 绑定枚举值、显示枚举名称等
/// </remarks>
public class EnumToStringConverter : IValueConverter
{
    /// <summary>
    /// 将枚举值转换为字符串
    /// </summary>
    /// <param name="value">枚举值</param>
    /// <param name="targetType">目标类型</param>
    /// <param name="parameter">参数（未使用）</param>
    /// <param name="culture">文化信息</param>
    /// <returns>枚举值的字符串表示</returns>
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null)
            return string.Empty;

        return value.ToString() ?? string.Empty;
    }

    /// <summary>
    /// 将字符串转换回枚举值
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="targetType">目标枚举类型</param>
    /// <param name="parameter">参数（未使用）</param>
    /// <param name="culture">文化信息</param>
    /// <returns>解析后的枚举值</returns>
    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string stringValue && targetType.IsEnum)
        {
            try
            {
                return Enum.Parse(targetType, stringValue, true);
            }
            catch
            {
                // 如果解析失败，返回枚举的默认值
                return Enum.GetValues(targetType).GetValue(0);
            }
        }

        // 返回枚举的默认值
        if (targetType.IsEnum)
        {
            return Enum.GetValues(targetType).GetValue(0);
        }

        return Binding.DoNothing;
    }
}
