<!-- 高级面包屑导航示例 -->
<UserControl x:Class="WPFTest.Views.Navigation.AdvancedBreadcrumbExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 高级面包屑导航 -->
        <ui:BreadcrumbBar Grid.Row="0"
                         ItemsSource="{Binding BreadcrumbItems}"
                         Margin="20,10"
                         Background="{DynamicResource LayerFillColorDefaultBrush}"
                         BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                         BorderThickness="1"
                         CornerRadius="8"
                         Padding="12,10">
            
            <!-- 高级自定义项模板 -->
            <ui:BreadcrumbBar.ItemTemplate>
                <DataTemplate>
                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="8,4"
                            Margin="2">
                        <StackPanel Orientation="Horizontal">
                            <!-- 图标 -->
                            <TextBlock Text="{Binding Icon}"
                                      FontSize="16"
                                      Margin="0,0,6,0"
                                      VerticalAlignment="Center"
                                      Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            
                            <!-- 标题 -->
                            <TextBlock Text="{Binding Name}"
                                      FontSize="14"
                                      FontWeight="Medium"
                                      VerticalAlignment="Center"
                                      Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            
                            <!-- 描述（可选） -->
                            <TextBlock Text="{Binding Description}"
                                      FontSize="11"
                                      Margin="6,0,0,0"
                                      VerticalAlignment="Center"
                                      Opacity="0.7"
                                      Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                      Visibility="{Binding ShowDescriptions, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </StackPanel>
                    </Border>
                </DataTemplate>
            </ui:BreadcrumbBar.ItemTemplate>

            <!-- 交互行为 -->
            <b:Interaction.Triggers>
                <b:EventTrigger EventName="SelectionChanged">
                    <b:InvokeCommandAction Command="{Binding BreadcrumbSelectionChangedCommand}" 
                                          CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=ui:BreadcrumbBar}}"/>
                </b:EventTrigger>
            </b:Interaction.Triggers>
        </ui:BreadcrumbBar>

        <!-- 控制面板 -->
        <ui:Card Grid.Row="1" Margin="20,0,20,10" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 显示选项 -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="显示选项" FontWeight="Bold" Margin="0,0,0,8"/>
                    <CheckBox Content="显示图标" IsChecked="{Binding ShowIcons}" Margin="0,0,0,4"/>
                    <CheckBox Content="显示描述" IsChecked="{Binding ShowDescriptions}" Margin="0,0,0,4"/>
                    <CheckBox Content="紧凑模式" IsChecked="{Binding CompactMode}" Margin="0,0,0,4"/>
                </StackPanel>

                <!-- 导航历史 -->
                <StackPanel Grid.Column="1" Margin="16,0">
                    <TextBlock Text="导航历史" FontWeight="Bold" Margin="0,0,0,8"/>
                    <ListBox ItemsSource="{Binding NavigationHistory}" 
                             MaxHeight="100"
                             ScrollViewer.VerticalScrollBarVisibility="Auto">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Icon}" Margin="0,0,4,0"/>
                                    <TextBlock Text="{Binding Name}" FontSize="12"/>
                                    <TextBlock Text="{Binding Timestamp, StringFormat=' ({0:HH:mm:ss})'}" 
                                              FontSize="10" 
                                              Opacity="0.6" 
                                              Margin="4,0,0,0"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>

                <!-- 快速操作 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="快速操作" FontWeight="Bold" Margin="0,0,0,8"/>
                    <ui:Button Content="🏠 回到首页" 
                              Command="{Binding GoHomeCommand}"
                              Appearance="Secondary"
                              Margin="0,0,0,4"
                              HorizontalAlignment="Stretch"/>
                    <ui:Button Content="📋 复制路径" 
                              Command="{Binding CopyPathCommand}"
                              Appearance="Secondary"
                              Margin="0,0,0,4"
                              HorizontalAlignment="Stretch"/>
                    <ui:Button Content="🔄 刷新" 
                              Command="{Binding RefreshCommand}"
                              Appearance="Secondary"
                              Margin="0,0,0,4"
                              HorizontalAlignment="Stretch"/>
                </StackPanel>
            </Grid>
        </ui:Card>

        <!-- 内容区域 -->
        <ScrollViewer Grid.Row="2" Margin="20,0,20,20">
            <StackPanel>
                <!-- 当前状态 -->
                <ui:Card Margin="0,0,0,16" Padding="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="当前状态" FontWeight="Bold" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding CurrentPath}" 
                                      FontFamily="Consolas"
                                      Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                      Padding="8,4"
                                      BorderRadius="4"
                                      Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding LastUpdated, StringFormat='最后更新: {0:yyyy-MM-dd HH:mm:ss}'}" 
                                      FontSize="12" 
                                      Opacity="0.7"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="16,0,0,0">
                            <TextBlock Text="统计信息" FontWeight="Bold" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding BreadcrumbItems.Count, StringFormat='面包屑项数: {0}'}" 
                                      FontSize="12" 
                                      Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding NavigationHistory.Count, StringFormat='历史记录: {0}'}" 
                                      FontSize="12" 
                                      Margin="0,0,0,2"/>
                            <TextBlock Text="{Binding TotalNavigations, StringFormat='总导航次数: {0}'}" 
                                      FontSize="12" 
                                      Margin="0,0,0,2"/>
                        </StackPanel>
                    </Grid>
                </ui:Card>

                <!-- 高级导航选项 -->
                <ui:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="高级导航" FontWeight="Bold" Margin="0,0,0,8"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="系统导航" FontSize="12" FontWeight="Medium" Margin="0,0,0,4"/>
                                <ui:Button Content="🏠 首页" Command="{Binding NavigateToCommand}" CommandParameter="home" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                                <ui:Button Content="⚙️ 设置" Command="{Binding NavigateToCommand}" CommandParameter="settings" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                                <ui:Button Content="ℹ️ 关于" Command="{Binding NavigateToCommand}" CommandParameter="about" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="8,0">
                                <TextBlock Text="项目管理" FontSize="12" FontWeight="Medium" Margin="0,0,0,4"/>
                                <ui:Button Content="📁 项目" Command="{Binding NavigateToCommand}" CommandParameter="projects" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                                <ui:Button Content="📄 文档" Command="{Binding NavigateToCommand}" CommandParameter="docs" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                                <ui:Button Content="🔧 工具" Command="{Binding NavigateToCommand}" CommandParameter="tools" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="团队协作" FontSize="12" FontWeight="Medium" Margin="0,0,0,4"/>
                                <ui:Button Content="👥 团队" Command="{Binding NavigateToCommand}" CommandParameter="team" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                                <ui:Button Content="💬 消息" Command="{Binding NavigateToCommand}" CommandParameter="messages" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                                <ui:Button Content="📊 报告" Command="{Binding NavigateToCommand}" CommandParameter="reports" Margin="0,0,0,4" HorizontalAlignment="Stretch"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </ui:Card>

                <!-- 自定义样式演示 -->
                <ui:Card Padding="16">
                    <StackPanel>
                        <TextBlock Text="自定义样式演示" FontWeight="Bold" Margin="0,0,0,8"/>
                        <TextBlock Text="这个面包屑导航演示了高级功能，包括自定义模板、交互行为、导航历史记录和实时统计信息。" 
                                  TextWrapping="Wrap" 
                                  Margin="0,0,0,8"/>
                        <TextBlock Text="特性包括：图标显示、描述文本、紧凑模式、导航历史、路径复制、实时统计等。" 
                                  TextWrapping="Wrap" 
                                  FontSize="12" 
                                  Opacity="0.8"/>
                    </StackPanel>
                </ui:Card>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
