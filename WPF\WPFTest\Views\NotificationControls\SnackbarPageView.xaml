<UserControl x:Class="WPFTest.Views.NotificationControls.SnackbarPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:viewmodels="clr-namespace:WPFTest.ViewModels.NotificationControls"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
             d:DataContext="{d:DesignInstance Type=viewmodels:SnackbarPageViewModel}"
             mc:Ignorable="d"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="2800" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="0,0,12,0">
        <StackPanel Margin="16">
            
            <!-- 页面标题 -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" 
                               Symbol="Comment24" 
                               FontSize="32" 
                               Margin="0,0,16,0"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="Snackbar 消息提示控件"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    <TextBlock Text="WPF-UI 轻量级消息提示控件，支持自定义依赖属性和高级功能"
                               FontSize="14"
                               Margin="0,4,0,0"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               TextWrapping="Wrap"/>
                </StackPanel>
            </Grid>

            <!-- Snackbar 基础功能 -->
            <ui:CardExpander Header="🎯 基础功能演示" IsExpanded="True" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="展示 Snackbar 的基础功能和不同消息类型"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- Snackbar 容器 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="Snackbar 演示区域：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- Snackbar 显示区域 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    MinHeight="100"
                                    Margin="0,0,0,16">
                                <Grid>
                                    <TextBlock Text="Snackbar 将在此区域显示"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Visibility="{Binding IsAnySnackbarVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                                    
                                    <!-- Snackbar 模拟实现 -->
                                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                                            CornerRadius="8"
                                            Padding="16,12"
                                            Margin="8"
                                            Visibility="{Binding IsSnackbarVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 图标 -->
                                            <ui:SymbolIcon Grid.Column="0"
                                                           Symbol="{Binding SnackbarIcon}"
                                                           FontSize="20"
                                                           Foreground="White"
                                                           Margin="0,0,12,0"
                                                           VerticalAlignment="Center"/>

                                            <!-- 内容 -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding SnackbarTitle}"
                                                           FontWeight="Bold"
                                                           Foreground="White"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="{Binding SnackbarMessage}"
                                                           Foreground="White"
                                                           TextWrapping="Wrap"
                                                           Opacity="0.9"/>
                                            </StackPanel>

                                            <!-- 关闭按钮 -->
                                            <ui:Button Grid.Column="2"
                                                       Content="✕"
                                                       Command="{Binding HideSnackbarCommand}"
                                                       Appearance="Transparent"
                                                       Foreground="White"
                                                       Padding="8,4"
                                                       FontSize="12"
                                                       Margin="12,0,0,0"/>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </Border>
                            
                            <!-- 控制面板 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16">
                                <StackPanel>
                                    <TextBlock Text="🎛️ 控制面板：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <!-- 消息设置 -->
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="130"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" 
                                                   Text="消息内容：" 
                                                   VerticalAlignment="Center" 
                                                   Margin="0,0,8,0"/>
                                        
                                        <ui:TextBox Grid.Column="1" 
                                                    Text="{Binding CustomMessage, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    PlaceholderText="输入自定义消息内容"
                                                    Margin="0,0,8,0"/>
                                        
                                        <TextBlock Grid.Column="2" 
                                                   Text="超时时间：" 
                                                   VerticalAlignment="Center" 
                                                   Margin="0,0,8,0"/>
                                        
                                        <ui:NumberBox Grid.Column="3"
                                                      
                                                      Value="{Binding CustomTimeout, Mode=TwoWay}"
                                                      Minimum="1000"
                                                      Maximum="10000"/>
                                    </Grid>
                                    
                                    <!-- 操作按钮 -->
                                    <WrapPanel>
                                        <ui:Button Content="信息消息"
                                                   Icon="{ui:SymbolIcon Info24}"
                                                   Command="{Binding ShowInfoSnackbarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="成功消息"
                                                   Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                                   Command="{Binding ShowSuccessSnackbarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="警告消息"
                                                   Icon="{ui:SymbolIcon Warning24}"
                                                   Command="{Binding ShowWarningSnackbarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="错误消息"
                                                   Icon="{ui:SymbolIcon ErrorCircle24}"
                                                   Command="{Binding ShowErrorSnackbarCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="自定义消息"
                                                   Icon="{ui:SymbolIcon Settings24}"
                                                   Command="{Binding ShowCustomSnackbarCommand}"
                                                   Appearance="Secondary"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="隐藏消息"
                                                   Icon="{ui:SymbolIcon EyeOff24}"
                                                   Command="{Binding HideSnackbarCommand}"
                                                   Appearance="Danger"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 基础代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="Snackbar 基础用法"
                        Language="XAML"
                        Description="展示 Snackbar 的基本 XAML 配置和属性绑定"
                        ShowTabs="True"
                        XamlCode="{Binding BasicXamlExample}"
                        CSharpCode="{Binding BasicCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

            <!-- 依赖属性深度解析 -->
            <ui:CardExpander Header="🔧 依赖属性深度解析" IsExpanded="True" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="深入了解 Snackbar 控件的依赖属性实现和自定义扩展"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- 依赖属性演示 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="📋 依赖属性实时演示：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 属性控制面板 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- Title 属性 -->
                                    <TextBlock Grid.Row="0" Grid.Column="0" 
                                               Text="Title 属性：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="0" Grid.Column="1" 
                                                Text="{Binding DemoTitle, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                Margin="0,0,16,8"/>
                                    
                                    <!-- Message 属性 -->
                                    <TextBlock Grid.Row="0" Grid.Column="2" 
                                               Text="Message 属性：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="0" Grid.Column="3" 
                                                Text="{Binding DemoMessage, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                Margin="0,0,0,8"/>
                                    
                                    <!-- Timeout 属性 -->
                                    <TextBlock Grid.Row="1" Grid.Column="0" 
                                               Text="Timeout 属性：" 
                                               VerticalAlignment="Center"/>
                                    <ui:NumberBox Grid.Row="1" Grid.Column="1" 
                                                  Value="{Binding DemoTimeout, Mode=TwoWay}"
                                                  Minimum="1000"
                                                  Maximum="10000"
                                                  Margin="0,0,16,8"/>
                                    
                                    <!-- Appearance 属性 -->
                                    <TextBlock Grid.Row="1" Grid.Column="2" 
                                               Text="Appearance：" 
                                               VerticalAlignment="Center"/>
                                    <ComboBox Grid.Row="1" Grid.Column="3" 
                                              SelectedItem="{Binding DemoAppearance, Mode=TwoWay}"
                                              ItemsSource="{Binding AvailableAppearances}"
                                              Margin="0,0,0,8"/>
                                    
                                    <!-- 实时预览按钮 -->
                                    <ui:Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                                               Content="🔄 实时预览依赖属性"
                                               Icon="{ui:SymbolIcon Play24}"
                                               Command="{Binding ShowDemoSnackbarCommand}"
                                               Appearance="Primary"
                                               Margin="0,8,8,0"/>
                                    
                                    <!-- 属性信息显示 -->
                                    <TextBlock Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2"
                                               Text="{Binding PropertyInfo}"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               TextWrapping="Wrap"
                                               Margin="8,8,0,0"/>
                                </Grid>
                            </Border>
                            
                            <!-- 演示 Snackbar -->
                            <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                                    CornerRadius="8"
                                    Padding="16,12"
                                    Margin="8"
                                    Visibility="{Binding IsDemoSnackbarVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 图标 -->
                                    <ui:SymbolIcon Grid.Column="0"
                                                   Symbol="Settings24"
                                                   FontSize="20"
                                                   Foreground="White"
                                                   Margin="0,0,12,0"
                                                   VerticalAlignment="Center"/>

                                    <!-- 内容 -->
                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock Text="{Binding DemoTitle}"
                                                   FontWeight="Bold"
                                                   Foreground="White"
                                                   Margin="0,0,0,4"/>
                                        <TextBlock Text="{Binding DemoMessage}"
                                                   Foreground="White"
                                                   TextWrapping="Wrap"
                                                   Opacity="0.9"/>
                                    </StackPanel>

                                    <!-- 关闭按钮 -->
                                    <ui:Button Grid.Column="2"
                                               Content="✕"
                                               Command="{Binding HideSnackbarCommand}"
                                               Appearance="Transparent"
                                               Foreground="White"
                                               Padding="8,4"
                                               FontSize="12"
                                               Margin="12,0,0,0"/>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 依赖属性代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="依赖属性深度实现"
                        Language="C#"
                        Description="展示如何创建自定义依赖属性和属性变化回调"
                        ShowTabs="True"
                        XamlCode="{Binding DependencyPropertyXamlExample}"
                        CSharpCode="{Binding DependencyPropertyCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

            <!-- 高级功能和自定义扩展 -->
            <ui:CardExpander Header="⚡ 高级功能和自定义扩展" IsExpanded="True" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="展示 Snackbar 的高级功能、自定义样式和扩展能力"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- 高级功能演示 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="🚀 高级功能演示：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 功能选项 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="功能配置：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <WrapPanel>
                                        <CheckBox Content="启用队列管理"
                                                  IsChecked="{Binding EnableQueue}"
                                                  Margin="0,0,16,8"/>
                                        <CheckBox Content="启用动画效果"
                                                  IsChecked="{Binding EnableAnimation}"
                                                  Margin="0,0,16,8"/>
                                        <CheckBox Content="启用声音提示"
                                                  IsChecked="{Binding EnableSound}"
                                                  Margin="0,0,16,8"/>
                                        <CheckBox Content="启用自动关闭"
                                                  IsChecked="{Binding EnableAutoClose}"
                                                  Margin="0,0,16,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                            
                            <!-- 高级操作按钮 -->
                            <WrapPanel>
                                <ui:Button Content="队列消息测试"
                                           Icon="{ui:SymbolIcon List24}"
                                           Command="{Binding ShowQueueMessagesCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="长文本消息"
                                           Icon="{ui:SymbolIcon Document24}"
                                           Command="{Binding ShowLongMessageCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="带操作按钮"
                                           Icon="{ui:SymbolIcon Play24}"
                                           Command="{Binding ShowActionSnackbarCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="自定义样式"
                                           Icon="{ui:SymbolIcon ColorBackground24}"
                                           Command="{Binding ShowCustomStyleCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="清空队列"
                                           Icon="{ui:SymbolIcon Delete24}"
                                           Command="{Binding ClearQueueCommand}"
                                           Appearance="Danger"
                                           Margin="0,0,8,8"/>
                            </WrapPanel>
                            
                            <!-- 状态显示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,16,0,0">
                                <StackPanel>
                                    <TextBlock Text="📊 状态信息：" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding StatusInfo}" 
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 高级功能代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="高级功能实现"
                        Language="C#"
                        Description="展示 Snackbar 的高级功能和自定义扩展实现"
                        ShowTabs="True"
                        XamlCode="{Binding AdvancedXamlExample}"
                        CSharpCode="{Binding AdvancedCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>
</UserControl>
