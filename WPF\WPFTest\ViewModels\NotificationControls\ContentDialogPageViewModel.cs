using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.NotificationControls;

/// <summary>
/// ContentDialog 页面 ViewModel - 展示 CommunityToolkit.Mvvm 深度集成和最佳实践
/// </summary>
public partial class ContentDialogPageViewModel : ObservableValidator, IRecipient<string>
{
    #region 私有字段

    private readonly YLoggerInstance _logger = YLogger.ForDebug<ContentDialogPageViewModel>();
    private readonly IContentDialogService _contentDialogService;
    private CancellationTokenSource? _asyncOperationCancellationTokenSource;
    private int _operationCounter = 0;

    #endregion

    #region 基础对话框属性

    [ObservableProperty]
    private string dialogTitle = "确认操作";

    [ObservableProperty]
    private string dialogContent = "您确定要执行此操作吗？";

    [ObservableProperty]
    private string primaryButtonText = "确定";

    [ObservableProperty]
    private string secondaryButtonText = "取消";

    [ObservableProperty]
    private string closeButtonText = "关闭";

    [ObservableProperty]
    private ContentDialogButton defaultButton = ContentDialogButton.Primary;

    [ObservableProperty]
    private ObservableCollection<ContentDialogButton> availableDefaultButtons = new()
    {
        ContentDialogButton.Primary,
        ContentDialogButton.Secondary,
        ContentDialogButton.Close
    };

    #endregion

    #region 对话框结果属性

    [ObservableProperty]
    private bool hasDialogResult = false;

    [ObservableProperty]
    private string dialogResultText = string.Empty;

    [ObservableProperty]
    private Brush dialogResultBrush = Brushes.Gray;

    #endregion

    #region 异步操作属性

    [ObservableProperty]
    private bool isAsyncOperationRunning = false;

    [ObservableProperty]
    private string asyncOperationStatus = string.Empty;

    [ObservableProperty]
    private double asyncOperationProgress = 0;

    [ObservableProperty]
    private bool isAsyncOperationIndeterminate = false;

    #endregion

    #region 数据验证属性

    [ObservableProperty]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = "输入内容不能为空")]
    [MinLength(3, ErrorMessage = "输入内容至少需要3个字符")]
    [MaxLength(100, ErrorMessage = "输入内容不能超过100个字符")]
    private string validationInput = string.Empty;

    [ObservableProperty]
    private string validationErrors = string.Empty;

    [ObservableProperty]
    private bool hasValidationErrors = false;

    #endregion

    #region 消息传递属性

    [ObservableProperty]
    private string lastReceivedMessage = string.Empty;

    [ObservableProperty]
    private bool hasReceivedMessage = false;

    #endregion

    #region 操作状态属性

    [ObservableProperty]
    private string operationStatus = "就绪 - 等待操作";

    #endregion

    #region 代码示例属性

    [ObservableProperty]
    private string basicXamlExample = string.Empty;

    [ObservableProperty]
    private string basicCSharpExample = string.Empty;

    [ObservableProperty]
    private string mvvmXamlExample = string.Empty;

    [ObservableProperty]
    private string mvvmCSharpExample = string.Empty;

    [ObservableProperty]
    private string advancedXamlExample = string.Empty;

    [ObservableProperty]
    private string advancedCSharpExample = string.Empty;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public ContentDialogPageViewModel()
    {
        try
        {
            _logger.Info("🚀 ContentDialogPageViewModel 构造函数开始");

            // 初始化 ContentDialogService
            _contentDialogService = new ContentDialogService();

            // 注册消息接收
            WeakReferenceMessenger.Default.Register<string>(this);

            // 初始化代码示例
            InitializeCodeExamples();

            _logger.Info("✅ ContentDialogPageViewModel 构造完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ ContentDialogPageViewModel 构造失败: {ex}");
        }
    }

    #endregion

    #region 基础对话框命令

    [RelayCommand]
    private async Task ShowInfoDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "信息提示",
            Content = "这是一个信息类型的对话框，用于显示重要信息。",
            PrimaryButtonText = "知道了",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);

        var result = await dialog.ShowAsync();
        UpdateDialogResult("信息对话框", result, Brushes.Blue);
        UpdateOperationStatus("显示信息对话框");
        _logger.Info("📢 显示信息对话框");
    }

    [RelayCommand]
    private async Task ShowConfirmDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "确认操作",
            Content = "您确定要执行此操作吗？此操作无法撤销。",
            PrimaryButtonText = "确定",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);

        var result = await dialog.ShowAsync();
        UpdateDialogResult("确认对话框", result, result == ContentDialogResult.Primary ? Brushes.Green : Brushes.Orange);
        UpdateOperationStatus("显示确认对话框");
        _logger.Info("❓ 显示确认对话框");
    }

    [RelayCommand]
    private async Task ShowWarningDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "警告提示",
            Content = "检测到潜在风险，建议您先备份数据再继续操作。",
            PrimaryButtonText = "继续",
            SecondaryButtonText = "取消",
            CloseButtonText = "稍后处理",
            DefaultButton = ContentDialogButton.Secondary
        };

        SetupContentDialog(dialog);

        var result = await dialog.ShowAsync();
        UpdateDialogResult("警告对话框", result, Brushes.Orange);
        UpdateOperationStatus("显示警告对话框");
        _logger.Info("⚠️ 显示警告对话框");
    }

    [RelayCommand]
    private async Task ShowErrorDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "错误信息",
            Content = "操作失败：网络连接超时。请检查网络设置后重试。",
            PrimaryButtonText = "重试",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);

        var result = await dialog.ShowAsync();
        UpdateDialogResult("错误对话框", result, Brushes.Red);
        UpdateOperationStatus("显示错误对话框");
        _logger.Info("❌ 显示错误对话框");
    }

    [RelayCommand]
    private async Task ShowCustomDialog()
    {
        var dialog = new ContentDialog
        {
            Title = string.IsNullOrWhiteSpace(DialogTitle) ? "自定义对话框" : DialogTitle,
            Content = string.IsNullOrWhiteSpace(DialogContent) ? "这是自定义内容" : DialogContent,
            PrimaryButtonText = string.IsNullOrWhiteSpace(PrimaryButtonText) ? "确定" : PrimaryButtonText,
            SecondaryButtonText = string.IsNullOrWhiteSpace(SecondaryButtonText) ? "取消" : SecondaryButtonText,
            CloseButtonText = string.IsNullOrWhiteSpace(CloseButtonText) ? "关闭" : CloseButtonText,
            DefaultButton = DefaultButton
        };

        SetupContentDialog(dialog);

        var result = await dialog.ShowAsync();
        UpdateDialogResult("自定义对话框", result, Brushes.Purple);
        UpdateOperationStatus("显示自定义对话框");
        _logger.Info($"🔧 显示自定义对话框: {DialogTitle}");
    }

    #endregion

    #region 异步操作命令

    [RelayCommand]
    private async Task ShowAsyncLoadingDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "异步加载",
            Content = "正在加载数据，请稍候...",
            PrimaryButtonText = "后台运行",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Secondary
        };

        SetupContentDialog(dialog);

        // 启动异步操作
        _asyncOperationCancellationTokenSource = new CancellationTokenSource();
        var loadingTask = SimulateAsyncLoading(_asyncOperationCancellationTokenSource.Token);

        var result = await dialog.ShowAsync();
        
        if (result == ContentDialogResult.Secondary)
        {
            _asyncOperationCancellationTokenSource.Cancel();
            UpdateOperationStatus("用户取消了异步加载操作");
        }
        else
        {
            UpdateOperationStatus("异步加载操作在后台继续运行");
        }

        UpdateDialogResult("异步加载对话框", result, Brushes.Blue);
        _logger.Info("🔄 显示异步加载对话框");
    }

    [RelayCommand]
    private async Task ShowAsyncSaveDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "保存数据",
            Content = "正在保存数据到服务器...",
            PrimaryButtonText = "等待完成",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);

        _asyncOperationCancellationTokenSource = new CancellationTokenSource();
        var savingTask = SimulateAsyncSaving(_asyncOperationCancellationTokenSource.Token);

        var result = await dialog.ShowAsync();
        
        if (result == ContentDialogResult.Secondary)
        {
            _asyncOperationCancellationTokenSource.Cancel();
        }

        UpdateDialogResult("异步保存对话框", result, Brushes.Green);
        UpdateOperationStatus("异步保存操作处理完成");
        _logger.Info("💾 显示异步保存对话框");
    }

    [RelayCommand]
    private void CancelAsyncOperation()
    {
        _asyncOperationCancellationTokenSource?.Cancel();
        IsAsyncOperationRunning = false;
        AsyncOperationStatus = "操作已取消";
        UpdateOperationStatus("用户取消了异步操作");
        _logger.Info("🚫 取消异步操作");
    }

    #endregion

    #region 数据验证命令

    [RelayCommand]
    private async Task ShowValidationDialog()
    {
        // 手动触发验证
        ValidateAllProperties();

        if (HasErrors)
        {
            var errors = GetErrors(nameof(ValidationInput))
                .Cast<System.ComponentModel.DataAnnotations.ValidationResult>()
                .Select(vr => vr.ErrorMessage ?? "未知错误")
                .ToList();
            ValidationErrors = string.Join(", ", errors);
            HasValidationErrors = true;

            var dialog = new ContentDialog
            {
                Title = "验证失败",
                Content = $"输入数据不符合要求：\n{ValidationErrors}",
                PrimaryButtonText = "知道了",
                DefaultButton = ContentDialogButton.Primary
            };

            SetupContentDialog(dialog);
            await dialog.ShowAsync();
            UpdateOperationStatus("数据验证失败");
        }
        else
        {
            HasValidationErrors = false;
            ValidationErrors = string.Empty;

            var dialog = new ContentDialog
            {
                Title = "验证成功",
                Content = $"输入数据验证通过：\n内容：{ValidationInput}\n长度：{ValidationInput.Length} 字符",
                PrimaryButtonText = "确定",
                DefaultButton = ContentDialogButton.Primary
            };

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();
            UpdateDialogResult("验证对话框", result, Brushes.Green);
            UpdateOperationStatus("数据验证成功");
        }

        _logger.Info($"✅ 数据验证: {ValidationInput}");
    }

    #endregion

    #region 消息传递命令

    [RelayCommand]
    private void SendGlobalMessage()
    {
        var message = $"全局消息 #{++_operationCounter} - {DateTime.Now:HH:mm:ss}";
        WeakReferenceMessenger.Default.Send(message);
        UpdateOperationStatus($"发送全局消息: {message}");
        _logger.Info($"📡 发送全局消息: {message}");
    }

    [RelayCommand]
    private async Task ShowMessageSubscriptionDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "消息订阅",
            Content = $"当前已接收到 {_operationCounter} 条消息。\n最后一条消息：{LastReceivedMessage}",
            PrimaryButtonText = "继续订阅",
            SecondaryButtonText = "取消订阅",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        
        if (result == ContentDialogResult.Secondary)
        {
            WeakReferenceMessenger.Default.Unregister<string>(this);
            UpdateOperationStatus("已取消消息订阅");
        }
        else
        {
            UpdateOperationStatus("继续订阅全局消息");
        }

        UpdateDialogResult("消息订阅对话框", result, Brushes.Cyan);
        _logger.Info("📧 显示消息订阅对话框");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 设置对话框的 ContentPresenter
    /// </summary>
    private void SetupContentDialog(ContentDialog dialog)
    {
        if (Application.Current.MainWindow != null)
        {
            var contentPresenter = Application.Current.MainWindow.FindName("RootContentDialogPresenter") as ContentPresenter;
            if (contentPresenter != null)
            {
#pragma warning disable CS0618 // 类型或成员已过时
                dialog.DialogHost = contentPresenter;
#pragma warning restore CS0618 // 类型或成员已过时
                _logger.Debug("✅ ContentDialog DialogHost 设置成功");
            }
            else
            {
                _logger.Warning("⚠️ 未找到 RootContentDialogPresenter");
            }
        }
        else
        {
            _logger.Warning("⚠️ MainWindow 为空");
        }
    }

    /// <summary>
    /// 更新对话框结果显示
    /// </summary>
    private void UpdateDialogResult(string dialogType, ContentDialogResult result, Brush brush)
    {
        var resultText = result switch
        {
            ContentDialogResult.Primary => "主要按钮",
            ContentDialogResult.Secondary => "次要按钮",
            ContentDialogResult.None => "关闭按钮",
            _ => "未知结果"
        };

        DialogResultText = $"{DateTime.Now:HH:mm:ss} - {dialogType}：用户点击了 {resultText}";
        DialogResultBrush = brush;
        HasDialogResult = true;
    }

    /// <summary>
    /// 更新操作状态
    /// </summary>
    private void UpdateOperationStatus(string status)
    {
        OperationStatus = $"{DateTime.Now:HH:mm:ss} - {status}";
    }

    /// <summary>
    /// 模拟异步加载
    /// </summary>
    private async Task SimulateAsyncLoading(CancellationToken cancellationToken)
    {
        try
        {
            IsAsyncOperationRunning = true;
            IsAsyncOperationIndeterminate = false;
            AsyncOperationStatus = "正在加载数据...";

            for (int i = 0; i <= 100; i += 10)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                AsyncOperationProgress = i;
                AsyncOperationStatus = $"加载进度: {i}%";
                
                await Task.Delay(500, cancellationToken);
            }

            AsyncOperationStatus = "数据加载完成";
            await Task.Delay(1000, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            AsyncOperationStatus = "加载操作已取消";
        }
        finally
        {
            IsAsyncOperationRunning = false;
        }
    }

    /// <summary>
    /// 模拟异步保存
    /// </summary>
    private async Task SimulateAsyncSaving(CancellationToken cancellationToken)
    {
        try
        {
            IsAsyncOperationRunning = true;
            IsAsyncOperationIndeterminate = true;
            AsyncOperationStatus = "正在保存数据到服务器...";

            await Task.Delay(3000, cancellationToken);

            IsAsyncOperationIndeterminate = false;
            AsyncOperationProgress = 100;
            AsyncOperationStatus = "数据保存完成";
            
            await Task.Delay(1000, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            AsyncOperationStatus = "保存操作已取消";
        }
        finally
        {
            IsAsyncOperationRunning = false;
        }
    }

    /// <summary>
    /// 接收全局消息
    /// </summary>
    public void Receive(string message)
    {
        LastReceivedMessage = message;
        HasReceivedMessage = true;
        _logger.Info($"📨 接收到全局消息: {message}");
    }

    #endregion

    #region 高级功能命令

    [RelayCommand]
    private async Task ShowFormDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "表单输入",
            Content = CreateFormContent(),
            PrimaryButtonText = "提交",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        UpdateDialogResult("表单对话框", result, Brushes.Blue);
        UpdateOperationStatus("显示表单输入对话框");
        _logger.Info("📝 显示表单对话框");
    }

    [RelayCommand]
    private async Task ShowProgressDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "处理进度",
            Content = CreateProgressContent(),
            PrimaryButtonText = "后台运行",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        UpdateDialogResult("进度对话框", result, Brushes.Green);
        UpdateOperationStatus("显示进度条对话框");
        _logger.Info("📊 显示进度对话框");
    }

    [RelayCommand]
    private async Task ShowListSelectionDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "选择项目",
            Content = CreateListSelectionContent(),
            PrimaryButtonText = "确定",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        UpdateDialogResult("列表选择对话框", result, Brushes.Orange);
        UpdateOperationStatus("显示列表选择对话框");
        _logger.Info("📋 显示列表选择对话框");
    }

    [RelayCommand]
    private async Task ShowImagePreviewDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "图片预览",
            Content = CreateImagePreviewContent(),
            PrimaryButtonText = "保存",
            SecondaryButtonText = "关闭",
            DefaultButton = ContentDialogButton.Secondary
        };

        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        UpdateDialogResult("图片预览对话框", result, Brushes.Purple);
        UpdateOperationStatus("显示图片预览对话框");
        _logger.Info("🖼️ 显示图片预览对话框");
    }

    [RelayCommand]
    private async Task ShowWizardDialog()
    {
        await ShowWizardStep1();
    }

    [RelayCommand]
    private async Task ShowDeleteConfirmationFlow()
    {
        var step1 = new ContentDialog
        {
            Title = "确认删除",
            Content = "您确定要删除选中的项目吗？此操作无法撤销。",
            PrimaryButtonText = "删除",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Secondary
        };

        SetupContentDialog(step1);
        var result1 = await step1.ShowAsync();

        if (result1 == ContentDialogResult.Primary)
        {
            var step2 = new ContentDialog
            {
                Title = "最终确认",
                Content = "这是最后一次确认。删除后数据将永久丢失，您真的确定吗？",
                PrimaryButtonText = "确定删除",
                SecondaryButtonText = "我再想想",
                DefaultButton = ContentDialogButton.Secondary
            };

            SetupContentDialog(step2);
            var result2 = await step2.ShowAsync();

            if (result2 == ContentDialogResult.Primary)
            {
                var step3 = new ContentDialog
                {
                    Title = "删除完成",
                    Content = "项目已成功删除。",
                    PrimaryButtonText = "知道了",
                    DefaultButton = ContentDialogButton.Primary
                };

                SetupContentDialog(step3);
                await step3.ShowAsync();
                UpdateOperationStatus("删除确认流程完成 - 项目已删除");
            }
            else
            {
                UpdateOperationStatus("删除确认流程取消 - 用户在最终确认时取消");
            }
        }
        else
        {
            UpdateOperationStatus("删除确认流程取消 - 用户在第一步取消");
        }

        _logger.Info("🗑️ 执行删除确认流程");
    }

    [RelayCommand]
    private async Task ShowSettingsFlow()
    {
        var step1 = new ContentDialog
        {
            Title = "设置向导 - 第1步",
            Content = "欢迎使用设置向导。我们将帮助您配置应用程序。",
            PrimaryButtonText = "下一步",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(step1);
        var result1 = await step1.ShowAsync();

        if (result1 == ContentDialogResult.Primary)
        {
            var step2 = new ContentDialog
            {
                Title = "设置向导 - 第2步",
                Content = "请选择您的首选项设置。",
                PrimaryButtonText = "下一步",
                SecondaryButtonText = "上一步",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Primary
            };

            SetupContentDialog(step2);
            var result2 = await step2.ShowAsync();

            if (result2 == ContentDialogResult.Primary)
            {
                var step3 = new ContentDialog
                {
                    Title = "设置向导 - 完成",
                    Content = "设置已保存。感谢您使用设置向导！",
                    PrimaryButtonText = "完成",
                    DefaultButton = ContentDialogButton.Primary
                };

                SetupContentDialog(step3);
                await step3.ShowAsync();
                UpdateOperationStatus("设置向导流程完成");
            }
            else if (result2 == ContentDialogResult.Secondary)
            {
                // 返回第一步
                await ShowSettingsFlow();
                return;
            }
        }

        UpdateOperationStatus("设置向导流程结束");
        _logger.Info("⚙️ 执行设置向导流程");
    }

    #endregion

    #region 辅助方法

    private object CreateFormContent()
    {
        // 这里应该创建一个包含表单控件的 UserControl
        // 为了简化，返回文本描述
        return "表单内容：\n• 姓名输入框\n• 邮箱输入框\n• 备注文本框\n• 选项复选框";
    }

    private object CreateProgressContent()
    {
        return "正在处理数据...\n进度：75%\n预计剩余时间：30秒";
    }

    private object CreateListSelectionContent()
    {
        return "请选择一个选项：\n☑ 选项 1\n☐ 选项 2\n☐ 选项 3\n☐ 选项 4";
    }

    private object CreateImagePreviewContent()
    {
        return "图片预览区域\n文件名：sample.jpg\n尺寸：1920x1080\n大小：2.5 MB";
    }

    private async Task ShowWizardStep1()
    {
        var step1 = new ContentDialog
        {
            Title = "向导 - 步骤 1/3",
            Content = "这是向导的第一步。请选择您要执行的操作类型。",
            PrimaryButtonText = "下一步",
            SecondaryButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(step1);
        var result = await step1.ShowAsync();

        if (result == ContentDialogResult.Primary)
        {
            await ShowWizardStep2();
        }
        else
        {
            UpdateOperationStatus("向导流程已取消");
        }
    }

    private async Task ShowWizardStep2()
    {
        var step2 = new ContentDialog
        {
            Title = "向导 - 步骤 2/3",
            Content = "请配置详细参数。这些设置将影响后续操作。",
            PrimaryButtonText = "下一步",
            SecondaryButtonText = "上一步",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(step2);
        var result = await step2.ShowAsync();

        if (result == ContentDialogResult.Primary)
        {
            await ShowWizardStep3();
        }
        else if (result == ContentDialogResult.Secondary)
        {
            await ShowWizardStep1();
        }
        else
        {
            UpdateOperationStatus("向导流程已取消");
        }
    }

    private async Task ShowWizardStep3()
    {
        var step3 = new ContentDialog
        {
            Title = "向导 - 步骤 3/3",
            Content = "确认您的设置并完成向导。",
            PrimaryButtonText = "完成",
            SecondaryButtonText = "上一步",
            CloseButtonText = "取消",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(step3);
        var result = await step3.ShowAsync();

        if (result == ContentDialogResult.Primary)
        {
            UpdateOperationStatus("向导流程已完成");
            var completion = new ContentDialog
            {
                Title = "完成",
                Content = "向导已成功完成！所有设置已保存。",
                PrimaryButtonText = "知道了",
                DefaultButton = ContentDialogButton.Primary
            };
            SetupContentDialog(completion);
            await completion.ShowAsync();
        }
        else if (result == ContentDialogResult.Secondary)
        {
            await ShowWizardStep2();
        }
        else
        {
            UpdateOperationStatus("向导流程已取消");
        }
    }

    #endregion

    #region 代码示例初始化

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamples()
    {
        // 基础 XAML 示例
        BasicXamlExample = @"<!-- ContentDialog 基础用法 -->
<ui:Button Content=""显示对话框""
           Icon=""{ui:SymbolIcon Info24}""
           Command=""{Binding ShowInfoDialogCommand}""/>

<!-- 自定义配置 -->
<StackPanel>
    <ui:TextBox Text=""{Binding DialogTitle, Mode=TwoWay}""
                PlaceholderText=""对话框标题""/>
    <ui:TextBox Text=""{Binding DialogContent, Mode=TwoWay}""
                PlaceholderText=""对话框内容""/>
    <ComboBox SelectedItem=""{Binding DefaultButton, Mode=TwoWay}""
              ItemsSource=""{Binding AvailableDefaultButtons}""/>
</StackPanel>";

        // 基础 C# 示例
        BasicCSharpExample = @"// ContentDialog 基础控制 - 使用 CommunityToolkit.Mvvm
public partial class ContentDialogPageViewModel : ObservableObject
{
    [ObservableProperty]
    private string dialogTitle = ""确认操作"";

    [ObservableProperty]
    private string dialogContent = ""您确定要执行此操作吗？"";

    [ObservableProperty]
    private ContentDialogButton defaultButton = ContentDialogButton.Primary;

    [RelayCommand]
    private async Task ShowInfoDialog()
    {
        var dialog = new ContentDialog
        {
            Title = ""信息提示"",
            Content = ""这是一个信息类型的对话框"",
            PrimaryButtonText = ""知道了"",
            DefaultButton = ContentDialogButton.Primary
        };

        var result = await dialog.ShowAsync();

        // 处理对话框结果
        HandleDialogResult(result);
    }

    private void HandleDialogResult(ContentDialogResult result)
    {
        var resultText = result switch
        {
            ContentDialogResult.Primary => ""用户点击了主要按钮"",
            ContentDialogResult.Secondary => ""用户点击了次要按钮"",
            ContentDialogResult.None => ""用户点击了关闭按钮"",
            _ => ""未知结果""
        };

        // 更新 UI 状态
        UpdateStatus(resultText);
    }
}";

        // MVVM XAML 示例
        MvvmXamlExample = @"<!-- CommunityToolkit.Mvvm 深度集成 -->
<StackPanel>
    <!-- 异步命令演示 -->
    <ui:Button Content=""异步加载对话框""
               Icon=""{ui:SymbolIcon ArrowDownload24}""
               Command=""{Binding ShowAsyncLoadingDialogCommand}""/>

    <!-- 进度显示 -->
    <StackPanel Visibility=""{Binding IsAsyncOperationRunning,
                              Converter={StaticResource BooleanToVisibilityConverter}}"">
        <TextBlock Text=""{Binding AsyncOperationStatus}""/>
        <ProgressBar Value=""{Binding AsyncOperationProgress}""
                     Maximum=""100""
                     IsIndeterminate=""{Binding IsAsyncOperationIndeterminate}""/>
    </StackPanel>

    <!-- 数据验证演示 -->
    <ui:TextBox Text=""{Binding ValidationInput, Mode=TwoWay,
                        UpdateSourceTrigger=PropertyChanged}""
                PlaceholderText=""输入要验证的内容（至少3个字符）""/>
    <TextBlock Text=""{Binding ValidationErrors}""
               Foreground=""Red""
               Visibility=""{Binding HasValidationErrors,
                            Converter={StaticResource BooleanToVisibilityConverter}}""/>
    <ui:Button Content=""验证并显示对话框""
               Command=""{Binding ShowValidationDialogCommand}""/>

    <!-- 消息传递演示 -->
    <ui:Button Content=""发送全局消息""
               Command=""{Binding SendGlobalMessageCommand}""/>
    <TextBlock Text=""{Binding LastReceivedMessage}""
               Visibility=""{Binding HasReceivedMessage,
                            Converter={StaticResource BooleanToVisibilityConverter}}""/>
</StackPanel>";

        // MVVM C# 示例
        MvvmCSharpExample = @"// CommunityToolkit.Mvvm 深度集成示例
// ⚠️ 重要：每个 ContentDialog 都必须调用 SetupContentDialog(dialog) 才能正常显示！
public partial class ContentDialogPageViewModel : ObservableValidator, IRecipient<string>
{
    #region 异步操作属性
    [ObservableProperty]
    private bool isAsyncOperationRunning = false;

    [ObservableProperty]
    private string asyncOperationStatus = string.Empty;

    [ObservableProperty]
    private double asyncOperationProgress = 0;

    [ObservableProperty]
    private bool isAsyncOperationIndeterminate = false;
    #endregion

    #region 数据验证属性
    [ObservableProperty]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = ""输入内容不能为空"")]
    [MinLength(3, ErrorMessage = ""输入内容至少需要3个字符"")]
    [MaxLength(100, ErrorMessage = ""输入内容不能超过100个字符"")]
    private string validationInput = string.Empty;

    [ObservableProperty]
    private string validationErrors = string.Empty;

    [ObservableProperty]
    private bool hasValidationErrors = false;
    #endregion

    #region 消息传递属性
    [ObservableProperty]
    private string lastReceivedMessage = string.Empty;

    [ObservableProperty]
    private bool hasReceivedMessage = false;
    #endregion

    public ContentDialogPageViewModel()
    {
        // 注册消息接收
        WeakReferenceMessenger.Default.Register<string>(this);
    }

    #region 异步命令
    [RelayCommand]
    private async Task ShowAsyncLoadingDialog()
    {
        var dialog = new ContentDialog
        {
            Title = ""异步加载"",
            Content = ""正在加载数据，请稍候..."",
            PrimaryButtonText = ""后台运行"",
            SecondaryButtonText = ""取消"",
            DefaultButton = ContentDialogButton.Secondary
        };

        SetupContentDialog(dialog); // 必须调用！

        // 启动异步操作
        var cancellationTokenSource = new CancellationTokenSource();
        var loadingTask = SimulateAsyncLoading(cancellationTokenSource.Token);

        var result = await dialog.ShowAsync();

        if (result == ContentDialogResult.Secondary)
        {
            cancellationTokenSource.Cancel();
        }
    }

    private async Task SimulateAsyncLoading(CancellationToken cancellationToken)
    {
        try
        {
            IsAsyncOperationRunning = true;
            IsAsyncOperationIndeterminate = false;
            AsyncOperationStatus = ""正在加载数据..."";

            for (int i = 0; i <= 100; i += 10)
            {
                cancellationToken.ThrowIfCancellationRequested();

                AsyncOperationProgress = i;
                AsyncOperationStatus = $""加载进度: {i}%"";

                await Task.Delay(500, cancellationToken);
            }

            AsyncOperationStatus = ""数据加载完成"";
        }
        catch (OperationCanceledException)
        {
            AsyncOperationStatus = ""加载操作已取消"";
        }
        finally
        {
            IsAsyncOperationRunning = false;
        }
    }
    #endregion

    #region 数据验证命令
    [RelayCommand]
    private async Task ShowValidationDialog()
    {
        // 手动触发验证
        ValidateAllProperties();

        if (HasErrors)
        {
            var errors = GetErrors(nameof(ValidationInput))
                .Cast<System.ComponentModel.DataAnnotations.ValidationResult>()
                .Select(vr => vr.ErrorMessage ?? ""未知错误"")
                .ToList();
            ValidationErrors = string.Join("", "", errors);
            HasValidationErrors = true;

            var dialog = new ContentDialog
            {
                Title = ""验证失败"",
                Content = $""输入数据不符合要求：\n{ValidationErrors}"",
                PrimaryButtonText = ""知道了"",
                DefaultButton = ContentDialogButton.Primary
            };

            SetupContentDialog(dialog); // 必须调用！
            await dialog.ShowAsync();
        }
        else
        {
            HasValidationErrors = false;
            ValidationErrors = string.Empty;

            var dialog = new ContentDialog
            {
                Title = ""验证成功"",
                Content = $""输入数据验证通过：\n内容：{ValidationInput}\n长度：{ValidationInput.Length} 字符"",
                PrimaryButtonText = ""确定"",
                DefaultButton = ContentDialogButton.Primary
            };

            SetupContentDialog(dialog); // 必须调用！
            await dialog.ShowAsync();
        }
    }
    #endregion

    #region 消息传递
    [RelayCommand]
    private void SendGlobalMessage()
    {
        var message = $""全局消息 - {DateTime.Now:HH:mm:ss}"";
        WeakReferenceMessenger.Default.Send(message);
    }

    // 实现 IRecipient<string> 接口
    public void Receive(string message)
    {
        LastReceivedMessage = message;
        HasReceivedMessage = true;
    }
    #endregion
}";

        // 高级功能 XAML 示例
        AdvancedXamlExample = @"<!-- 高级对话框功能 -->
<StackPanel>
    <!-- 自定义内容对话框 -->
    <WrapPanel>
        <ui:Button Content=""表单输入对话框""
                   Icon=""{ui:SymbolIcon Form24}""
                   Command=""{Binding ShowFormDialogCommand}""/>
        <ui:Button Content=""进度条对话框""
                   Icon=""{ui:SymbolIcon Timer24}""
                   Command=""{Binding ShowProgressDialogCommand}""/>
        <ui:Button Content=""列表选择对话框""
                   Icon=""{ui:SymbolIcon List24}""
                   Command=""{Binding ShowListSelectionDialogCommand}""/>
        <ui:Button Content=""图片预览对话框""
                   Icon=""{ui:SymbolIcon Image24}""
                   Command=""{Binding ShowImagePreviewDialogCommand}""/>
    </WrapPanel>

    <!-- 对话框链式调用 -->
    <WrapPanel Margin=""0,16,0,0"">
        <ui:Button Content=""向导式对话框""
                   Icon=""{ui:SymbolIcon Navigation24}""
                   Command=""{Binding ShowWizardDialogCommand}""/>
        <ui:Button Content=""确认删除流程""
                   Icon=""{ui:SymbolIcon Delete24}""
                   Command=""{Binding ShowDeleteConfirmationFlowCommand}""/>
        <ui:Button Content=""设置配置流程""
                   Icon=""{ui:SymbolIcon Settings24}""
                   Command=""{Binding ShowSettingsFlowCommand}""/>
    </WrapPanel>

    <!-- 状态显示 -->
    <Border Background=""{DynamicResource LayerFillColorDefaultBrush}""
            CornerRadius=""8""
            Padding=""16""
            Margin=""0,16,0,0"">
        <StackPanel>
            <TextBlock Text=""📊 操作状态："" FontWeight=""Medium""/>
            <TextBlock Text=""{Binding OperationStatus}""
                       FontSize=""12""
                       Foreground=""{DynamicResource TextFillColorSecondaryBrush}""/>
        </StackPanel>
    </Border>
</StackPanel>";

        // 高级功能 C# 示例
        AdvancedCSharpExample = @"// 高级对话框功能实现
// ⚠️ 重要：每个 ContentDialog 都必须调用 SetupContentDialog(dialog) 才能正常显示！
public partial class ContentDialogPageViewModel : ObservableValidator
{
    #region 高级功能命令

    [RelayCommand]
    private async Task ShowFormDialog()
    {
        var dialog = new ContentDialog
        {
            Title = ""表单输入"",
            Content = CreateFormContent(), // 自定义内容
            PrimaryButtonText = ""提交"",
            SecondaryButtonText = ""取消"",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog); // 必须调用！
        var result = await dialog.ShowAsync();
        HandleFormResult(result);
    }

    [RelayCommand]
    private async Task ShowProgressDialog()
    {
        var dialog = new ContentDialog
        {
            Title = ""处理进度"",
            Content = CreateProgressContent(), // 包含进度条的自定义内容
            PrimaryButtonText = ""后台运行"",
            SecondaryButtonText = ""取消"",
            DefaultButton = ContentDialogButton.Primary
        };

        SetupContentDialog(dialog); // 必须调用！
        var result = await dialog.ShowAsync();
        HandleProgressResult(result);
    }

    // 对话框链式调用示例
    [RelayCommand]
    private async Task ShowWizardDialog()
    {
        await ShowWizardStep1();
    }

    private async Task ShowWizardStep1()
    {
        var step1 = new ContentDialog
        {
            Title = ""向导 - 步骤 1/3"",
            Content = ""这是向导的第一步。请选择您要执行的操作类型。"",
            PrimaryButtonText = ""下一步"",
            SecondaryButtonText = ""取消"",
            DefaultButton = ContentDialogButton.Primary
        };

        var result = await step1.ShowAsync();

        if (result == ContentDialogResult.Primary)
        {
            await ShowWizardStep2();
        }
    }

    private async Task ShowWizardStep2()
    {
        var step2 = new ContentDialog
        {
            Title = ""向导 - 步骤 2/3"",
            Content = ""请配置详细参数。这些设置将影响后续操作。"",
            PrimaryButtonText = ""下一步"",
            SecondaryButtonText = ""上一步"",
            CloseButtonText = ""取消"",
            DefaultButton = ContentDialogButton.Primary
        };

        var result = await step2.ShowAsync();

        if (result == ContentDialogResult.Primary)
        {
            await ShowWizardStep3();
        }
        else if (result == ContentDialogResult.Secondary)
        {
            await ShowWizardStep1(); // 返回上一步
        }
    }

    // 删除确认流程
    [RelayCommand]
    private async Task ShowDeleteConfirmationFlow()
    {
        var step1 = new ContentDialog
        {
            Title = ""确认删除"",
            Content = ""您确定要删除选中的项目吗？此操作无法撤销。"",
            PrimaryButtonText = ""删除"",
            SecondaryButtonText = ""取消"",
            DefaultButton = ContentDialogButton.Secondary
        };

        var result1 = await step1.ShowAsync();

        if (result1 == ContentDialogResult.Primary)
        {
            var step2 = new ContentDialog
            {
                Title = ""最终确认"",
                Content = ""这是最后一次确认。删除后数据将永久丢失，您真的确定吗？"",
                PrimaryButtonText = ""确定删除"",
                SecondaryButtonText = ""我再想想"",
                DefaultButton = ContentDialogButton.Secondary
            };

            var result2 = await step2.ShowAsync();

            if (result2 == ContentDialogResult.Primary)
            {
                // 执行删除操作
                await PerformDeleteOperation();

                var completion = new ContentDialog
                {
                    Title = ""删除完成"",
                    Content = ""项目已成功删除。"",
                    PrimaryButtonText = ""知道了"",
                    DefaultButton = ContentDialogButton.Primary
                };

                await completion.ShowAsync();
                UpdateOperationStatus(""删除操作完成"");
            }
        }
    }

    private async Task PerformDeleteOperation()
    {
        // 模拟删除操作
        UpdateOperationStatus(""正在删除项目..."");
        await Task.Delay(2000); // 模拟耗时操作
        UpdateOperationStatus(""项目删除完成"");
    }

    // 自定义内容创建方法
    private object CreateFormContent()
    {
        // 在实际应用中，这里应该返回一个包含表单控件的 UserControl
        return ""表单内容：\n• 姓名输入框\n• 邮箱输入框\n• 备注文本框\n• 选项复选框"";
    }

    private object CreateProgressContent()
    {
        // 在实际应用中，这里应该返回包含 ProgressBar 的 UserControl
        return ""正在处理数据...\n进度：75%\n预计剩余时间：30秒"";
    }

    /// <summary>
    /// 设置对话框的 ContentPresenter - 必须调用！
    /// </summary>
    private void SetupContentDialog(ContentDialog dialog)
    {
        if (Application.Current.MainWindow != null)
        {
            var contentPresenter = Application.Current.MainWindow.FindName(""RootContentDialogPresenter"") as ContentPresenter;
            if (contentPresenter != null)
            {
#pragma warning disable CS0618 // 类型或成员已过时
                dialog.DialogHost = contentPresenter;
#pragma warning restore CS0618 // 类型或成员已过时
            }
        }
    }
    #endregion
}";
    }

    #endregion
}
