namespace WPFTest.Models.DWG;

/// <summary>
/// 图标选项模型 - 用于图标选择器
/// </summary>
public class IconOption
{
    /// <summary>
    /// 图标字符
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 图标名称/描述
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 图标分类
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 构造函数
    /// </summary>
    public IconOption(string icon, string name, string category = "通用")
    {
        Icon = icon;
        Name = name;
        Category = category;
    }

    /// <summary>
    /// 获取默认图标选项列表
    /// </summary>
    public static List<IconOption> GetDefaultIcons()
    {
        return new List<IconOption>
        {
            // 建筑类图标
            new("🏗️", "施工图", "建筑"),
            new("🏢", "建筑物", "建筑"),
            new("🏠", "住宅", "建筑"),
            new("🏭", "工厂", "建筑"),
            new("🏛️", "古典建筑", "建筑"),
            new("🏘️", "住宅区", "建筑"),
            new("🏪", "商店", "建筑"),
            new("🏬", "百货商店", "建筑"),
            
            // 工程类图标
            new("📐", "设计图", "工程"),
            new("📏", "测量", "工程"),
            new("🔧", "工具", "工程"),
            new("⚙️", "机械", "工程"),
            new("🔩", "螺丝", "工程"),
            new("🛠️", "维修", "工程"),
            new("⚡", "电气", "工程"),
            new("💡", "照明", "工程"),
            
            // 文档类图标
            new("📋", "清单", "文档"),
            new("📄", "文档", "文档"),
            new("📊", "图表", "文档"),
            new("📈", "统计", "文档"),
            new("📉", "分析", "文档"),
            new("📝", "记录", "文档"),
            new("📑", "报告", "文档"),
            new("📓", "手册", "文档"),
            
            // 地图类图标
            new("🗺️", "地图", "地图"),
            new("🧭", "导航", "地图"),
            new("📍", "位置", "地图"),
            new("🌍", "全球", "地图"),
            new("🌎", "地球", "地图"),
            new("🗾", "区域图", "地图"),
            
            // 系统类图标
            new("📁", "文件夹", "系统"),
            new("📂", "打开文件夹", "系统"),
            new("🗂️", "文件盒", "系统"),
            new("🗃️", "文件柜", "系统"),
            new("💾", "保存", "系统"),
            new("💿", "光盘", "系统"),
            new("🖥️", "电脑", "系统"),
            new("🖨️", "打印机", "系统"),
            
            // 标记类图标
            new("⭐", "重要", "标记"),
            new("🔥", "热门", "标记"),
            new("✅", "完成", "标记"),
            new("❌", "错误", "标记"),
            new("⚠️", "警告", "标记"),
            new("ℹ️", "信息", "标记"),
            new("🔴", "红色", "标记"),
            new("🟢", "绿色", "标记"),
            new("🔵", "蓝色", "标记"),
            new("🟡", "黄色", "标记"),
            new("🟠", "橙色", "标记"),
            new("🟣", "紫色", "标记"),
            
            // 形状类图标
            new("⬛", "黑色方块", "形状"),
            new("⬜", "白色方块", "形状"),
            new("🔶", "橙色菱形", "形状"),
            new("🔷", "蓝色菱形", "形状"),
            new("🔸", "小橙色菱形", "形状"),
            new("🔹", "小蓝色菱形", "形状"),
            new("🔺", "红色三角", "形状"),
            new("🔻", "红色倒三角", "形状"),
            
            // 其他常用图标
            new("📦", "包装", "其他"),
            new("🎯", "目标", "其他"),
            new("🔍", "搜索", "其他"),
            new("🔎", "放大镜", "其他"),
            new("📌", "图钉", "其他"),
            new("📎", "回形针", "其他"),
            new("🔗", "链接", "其他"),
            new("⚡", "闪电", "其他"),
        };
    }

    /// <summary>
    /// 根据分类获取图标
    /// </summary>
    public static List<IconOption> GetIconsByCategory(string category)
    {
        return GetDefaultIcons().Where(icon => icon.Category == category).ToList();
    }

    /// <summary>
    /// 获取所有分类
    /// </summary>
    public static List<string> GetCategories()
    {
        return GetDefaultIcons().Select(icon => icon.Category).Distinct().OrderBy(c => c).ToList();
    }
}
