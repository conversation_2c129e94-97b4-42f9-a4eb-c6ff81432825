using System.IO;
using System.Runtime.InteropServices;
using Zylo.WPF.Controls.YFile.Model;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Services.YFile;

/// <summary>
/// 简化的文件拖拽服务 - 基于 DwgFileDragService 实现
/// </summary>
public class FileDragService
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<FileDragService>();

    /// <summary>
    /// 使用Windows底层API进行文件拖拽 - 与DwgFileDragService相同的实现
    /// </summary>
    public bool StartWindowsApiDrag(FileItem fileItem)
    {
        try
        {
            if (fileItem == null)
            {
                _logger.Warning("❌ 文件模型为空");
                return false;
            }

            var fullPath = Path.GetFullPath(fileItem.FullPath);

            if (!File.Exists(fullPath) && !Directory.Exists(fullPath))
            {
                _logger.Error($"❌ 文件不存在: {fullPath}");
                return false;
            }

            var fullFileName = Path.GetFileName(fullPath);
            _logger.Info($"🔧 使用Windows底层API拖拽: {fullFileName}");

            // 初始化OLE
            var hr = OleInitialize(IntPtr.Zero);
            if (hr != 0 && hr != 1)
            {
                _logger.Warning($"⚠️ OLE初始化异常: {hr}");
            }

            // 创建Shell文件数据对象
            var pidl = ILCreateFromPath(fullPath);
            if (pidl == IntPtr.Zero)
            {
                _logger.Error($"❌ 无法创建Shell路径: {fullPath}");
                return false;
            }

            try
            {
                // 分离父目录和文件项
                var pidlItem = ILFindLastID(pidl);
                var pidlItemClone = ILClone(pidlItem);
                ILRemoveLastID(pidl);
                var pidlParent = ILClone(pidl);

                var arrayPidl = new IntPtr[] { pidlItemClone };

                // 创建Shell标准数据对象
                System.Runtime.InteropServices.ComTypes.IDataObject dataObject = null;
                hr = SHCreateFileDataObject(pidlParent, 1u, arrayPidl, null, out dataObject);

                if (hr == 0 && dataObject != null)
                {
                    _logger.Info($"✅ Shell数据对象创建成功");

                    // 创建拖拽源
                    var dropSource = new WindowsDropSource();

                    // 执行拖拽操作
                    uint effect = 0;
                    hr = DoDragDrop(dataObject, dropSource,
                        (uint)(DragDropEffects.Copy | DragDropEffects.Move | DragDropEffects.Link),
                        out effect);

                    _logger.Info($"🎯 拖拽完成，结果: {hr}, 效果: {effect}");

                    // 清理资源
                    ILFree(pidlItemClone);
                    ILFree(pidlParent);

                    return hr == 0 || hr == 1;
                }
                else
                {
                    _logger.Error($"❌ Shell数据对象创建失败: {hr}");
                    return false;
                }
            }
            finally
            {
                ILFree(pidl);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ Windows API拖拽失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 创建拖拽数据对象
    /// </summary>
    public DataObject CreateDragDataObject(FileItem fileItem)
    {
        try
        {
            if (fileItem == null)
            {
                return new DataObject();
            }

            var fullPath = Path.GetFullPath(fileItem.FullPath);
            var dataObject = new DataObject();
            var filePaths = new[] { fullPath };

            dataObject.SetData(DataFormats.FileDrop, filePaths);
            dataObject.SetData(DataFormats.Text, fullPath);
            dataObject.SetData(DataFormats.UnicodeText, fullPath);

            return dataObject;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建拖拽数据对象失败: {ex.Message}");
            return new DataObject();
        }
    }

    #region Windows API 声明

    [DllImport("ole32.dll")]
    private static extern int OleInitialize(IntPtr pvReserved);

    [DllImport("shell32.dll", CharSet = CharSet.Unicode)]
    private static extern IntPtr ILCreateFromPath(string pszPath);

    [DllImport("shell32.dll")]
    private static extern IntPtr ILFindLastID(IntPtr pidl);

    [DllImport("shell32.dll")]
    private static extern IntPtr ILClone(IntPtr pidl);

    [DllImport("shell32.dll")]
    private static extern bool ILRemoveLastID(IntPtr pidl);

    [DllImport("shell32.dll")]
    private static extern void ILFree(IntPtr pidl);

    [DllImport("shell32.dll", EntryPoint = "#740")]
    private static extern int SHCreateFileDataObject(
        IntPtr pidlFolder,
        uint cidl,
        IntPtr[] apidl,
        System.Runtime.InteropServices.ComTypes.IDataObject pdtInner,
        out System.Runtime.InteropServices.ComTypes.IDataObject ppdtobj);

    [DllImport("ole32.dll", EntryPoint = "DoDragDrop")]
    private static extern int DoDragDrop(
        System.Runtime.InteropServices.ComTypes.IDataObject pDataObj,
        IDropSource pDropSource,
        uint dwOKEffects,
        out uint pdwEffect);

    #endregion
}

/// <summary>
/// Windows 拖拽源实现
/// </summary>
public class WindowsDropSource : IDropSource
{
    public int QueryContinueDrag(bool fEscapePressed, uint grfKeyState)
    {
        if (fEscapePressed) return 1; // DRAGDROP_S_CANCEL
        if ((grfKeyState & 0x01) == 0) return 0; // DRAGDROP_S_DROP
        return 2; // S_OK
    }

    public int GiveFeedback(uint dwEffect)
    {
        return 1; // DRAGDROP_S_USEDEFAULTCURSORS
    }
}

/// <summary>
/// Windows IDropSource 接口
/// </summary>
[ComImport, Guid("00000121-0000-0000-C000-000000000046"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IDropSource
{
    [PreserveSig]
    int QueryContinueDrag([MarshalAs(UnmanagedType.Bool)] bool fEscapePressed, uint grfKeyState);

    [PreserveSig]
    int GiveFeedback(uint dwEffect);
}
