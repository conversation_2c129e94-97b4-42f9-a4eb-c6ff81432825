using System.Windows.Controls;
using WPFTest.ViewModels.NotificationControls;
using Zylo.YLog.Runtime;

namespace WPFTest.Views.NotificationControls;

/// <summary>
/// MessageBoxPageView.xaml 的交互逻辑
/// </summary>
public partial class MessageBoxPageView : UserControl
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<MessageBoxPageView>();

    /// <summary>
    /// 获取或设置 ViewModel
    /// </summary>
    public MessageBoxPageViewModel ViewModel { get; }

    /// <summary>
    /// 初始化 MessageBoxPageView
    /// </summary>
    public MessageBoxPageView(MessageBoxPageViewModel viewModel)
    {
        ViewModel = viewModel;
        DataContext = ViewModel;
        
        InitializeComponent();
        
        _logger.Info("📦 MessageBoxPageView 初始化完成");
    }
}
