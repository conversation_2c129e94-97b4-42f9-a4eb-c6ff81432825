using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors.ListView;

/// <summary>
/// 鼠标滚轮滚动行为 - 支持鼠标中键滚动
/// </summary>
/// <remarks>
/// 🎯 功能特点：
/// - 支持鼠标滚轮垂直滚动
/// - 支持 Shift + 鼠标滚轮水平滚动
/// - 在水平滚动模式下直接支持水平滚动
/// - 自动检测滚动方向和模式
/// 
/// 🔧 使用方式：
/// ```xml
/// <ScrollViewer>
///     <b:Interaction.Behaviors>
///         <behaviors:MouseWheelScrollBehavior />
///     </b:Interaction.Behaviors>
/// </ScrollViewer>
/// ```
/// </remarks>
public class MouseWheelScrollBehavior : Behavior<ScrollViewer>
{
    protected override void OnAttached()
    {
        base.OnAttached();
        if (AssociatedObject != null)
        {
            AssociatedObject.PreviewMouseWheel += OnPreviewMouseWheel;
        }
    }

    protected override void OnDetaching()
    {
        if (AssociatedObject != null)
        {
            AssociatedObject.PreviewMouseWheel -= OnPreviewMouseWheel;
        }
        base.OnDetaching();
    }

    private void OnPreviewMouseWheel(object sender, MouseWheelEventArgs e)
    {
        if (sender is ScrollViewer scrollViewer)
        {
            // 检查是否按住Shift键进行水平滚动
            if (Keyboard.Modifiers == ModifierKeys.Shift)
            {
                // 水平滚动
                if (e.Delta > 0)
                {
                    scrollViewer.LineLeft();
                    scrollViewer.LineLeft();
                    scrollViewer.LineLeft();
                }
                else
                {
                    scrollViewer.LineRight();
                    scrollViewer.LineRight();
                    scrollViewer.LineRight();
                }
                e.Handled = true;
            }
            else
            {
                // 如果是水平滚动模式，直接进行水平滚动
                if (scrollViewer.HorizontalScrollBarVisibility != ScrollBarVisibility.Disabled &&
                    scrollViewer.VerticalScrollBarVisibility == ScrollBarVisibility.Disabled)
                {
                    if (e.Delta > 0)
                    {
                        scrollViewer.LineLeft();
                        scrollViewer.LineLeft();
                        scrollViewer.LineLeft();
                    }
                    else
                    {
                        scrollViewer.LineRight();
                        scrollViewer.LineRight();
                        scrollViewer.LineRight();
                    }
                    e.Handled = true;
                }
            }
        }
    }
}
