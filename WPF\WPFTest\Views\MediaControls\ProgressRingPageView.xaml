<UserControl
    d:DataContext="{d:DesignInstance mediaControls:ProgressRingPageViewModel}"
    d:DesignHeight="800"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.MediaControls.ProgressRingPageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mediaControls="clr-namespace:WPFTest.ViewModels.MediaControls"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:EnumToStringConverter x:Key="EnumToStringConverter" />

        <!--  标签文本样式  -->
        <Style TargetType="TextBlock" x:Key="LabelTextStyle">
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Margin" Value="0,8,0,4" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        </Style>

        <!--  数值输入框样式  -->
        <Style TargetType="ui:NumberBox" x:Key="NumberInputStyle">
            <Setter Property="Margin" Value="0,4,0,8" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="SpinButtonPlacementMode" Value="Inline" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer>
        <StackPanel Margin="24">

            <!--  页面标题  -->
            <StackPanel Margin="0,0,0,24">
                <TextBlock
                    FontSize="28"
                    FontWeight="Bold"
                    Text="ProgressRing 进度环控件" />
                <TextBlock
                    FontSize="14"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Margin="0,8,0,0"
                    Text="演示 WPF-UI ProgressRing 控件的各种功能和用法，包括确定和不确定进度模式。" />
            </StackPanel>

            <!--  状态栏  -->
            <Border
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                CornerRadius="8"
                Margin="0,0,0,24"
                Padding="16,12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <ui:SymbolIcon
                        FontSize="16"
                        Foreground="{DynamicResource SystemAccentColorSecondaryBrush}"
                        Grid.Column="0"
                        Symbol="Info24" />

                    <TextBlock
                        Grid.Column="1"
                        Margin="12,0,0,0"
                        Text="{Binding StatusMessage}"
                        VerticalAlignment="Center" />

                    <ui:ProgressRing
                        Grid.Column="2"
                        Height="20"
                        IsIndeterminate="True"
                        Visibility="{Binding IsSimulationRunning, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Width="20" />
                </Grid>
            </Border>

            <!--  基础功能演示  -->
            <ui:CardExpander
                Header="基础功能演示"
                Icon="{ui:SymbolIcon ArrowClockwise24}"
                IsExpanded="True"
                Style="{StaticResource ZyloDemoCardStyle}">
                <StackPanel>

                    <!--  主要演示区域  -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="300" />
                        </Grid.ColumnDefinitions>

                        <!--  进度环展示  -->
                        <Border
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            CornerRadius="12"
                            Grid.Column="0"
                            MinHeight="200"
                            Padding="32">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <!--  进度环  -->
                                <StackPanel
                                    Grid.Row="0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                                    <ui:ProgressRing
                                        Height="{Binding RingSize}"
                                        IsIndeterminate="{Binding IsIndeterminate}"
                                        Progress="{Binding ProgressValue}"
                                        Width="{Binding RingSize}" />

                                    <!--  进度文本  -->
                                    <TextBlock
                                        FontSize="18"
                                        FontWeight="SemiBold"
                                        HorizontalAlignment="Center"
                                        Margin="0,16,0,0"
                                        Text="{Binding ProgressText}"
                                        Visibility="{Binding ShowProgressText, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                </StackPanel>

                                <!--  控制按钮  -->
                                <StackPanel
                                    Grid.Row="1"
                                    HorizontalAlignment="Center"
                                    Orientation="Horizontal">
                                    <ui:Button
                                        Command="{Binding StartSimulationCommand}"
                                        Content="开始模拟"
                                        Icon="{ui:SymbolIcon Play24}"
                                        Margin="0,0,8,0" />
                                    <ui:Button
                                        Command="{Binding StopSimulationCommand}"
                                        Content="停止模拟"
                                        Icon="{ui:SymbolIcon Pause24}"
                                        Margin="0,0,8,0" />
                                    <ui:Button
                                        Command="{Binding ResetProgressCommand}"
                                        Content="重置"
                                        Icon="{ui:SymbolIcon ArrowReset24}"
                                        Margin="0,0,8,0" />
                                    <ui:Button
                                        Command="{Binding ToggleModeCommand}"
                                        Content="切换模式"
                                        Icon="{ui:SymbolIcon ArrowSwap24}" />
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!--  控制面板  -->
                        <StackPanel Grid.Column="1" Margin="16,0,0,0">

                            <!--  模式控制  -->
                            <GroupBox Header="模式控制">
                                <StackPanel>
                                    <CheckBox Content="不确定进度模式" IsChecked="{Binding IsIndeterminate}" />
                                    <CheckBox Content="显示进度文本" IsChecked="{Binding ShowProgressText}" />
                                </StackPanel>
                            </GroupBox>

                            <!--  进度控制  -->
                            <GroupBox Header="进度控制">
                                <StackPanel>
                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="当前进度" />
                                    <ui:NumberBox
                                        Maximum="100"
                                        Minimum="0"
                                        Style="{StaticResource NumberInputStyle}"
                                        Value="{Binding ProgressValue}" />

                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="最大值（计算用）" />
                                    <ui:NumberBox
                                        Maximum="1000"
                                        Minimum="1"
                                        Style="{StaticResource NumberInputStyle}"
                                        Value="{Binding MaximumValue}" />
                                </StackPanel>
                            </GroupBox>

                            <!--  外观控制  -->
                            <GroupBox Header="外观控制">
                                <StackPanel>
                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="大小预设" />
                                    <ComboBox ItemsSource="{Binding SizePresets}" SelectedItem="{Binding SelectedSizePreset}" />

                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="自定义大小" />
                                    <ui:NumberBox
                                        Maximum="200"
                                        Minimum="16"
                                        Style="{StaticResource NumberInputStyle}"
                                        Value="{Binding RingSize}" />

                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="厚度预设" />
                                    <ComboBox ItemsSource="{Binding ThicknessPresets}" SelectedItem="{Binding SelectedThicknessPreset}" />

                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="自定义厚度" />
                                    <ui:NumberBox
                                        Maximum="20"
                                        Minimum="1"
                                        Style="{StaticResource NumberInputStyle}"
                                        Value="{Binding StrokeThickness}" />
                                </StackPanel>
                            </GroupBox>

                            <!--  模拟控制  -->
                            <GroupBox Header="模拟控制">
                                <StackPanel>
                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="模拟速度 (毫秒)" />
                                    <ui:NumberBox
                                        Maximum="2000"
                                        Minimum="10"
                                        Style="{StaticResource NumberInputStyle}"
                                        Value="{Binding SimulationSpeed}" />

                                    <StackPanel Orientation="Horizontal">
                                        <ui:Button
                                            Command="{Binding SetProgressCommand}"
                                            CommandParameter="25"
                                            Content="25%"
                                            Margin="0,0,8,0" />
                                        <ui:Button
                                            Command="{Binding SetProgressCommand}"
                                            CommandParameter="50"
                                            Content="50%"
                                            Margin="0,0,8,0" />
                                        <ui:Button
                                            Command="{Binding SetProgressCommand}"
                                            CommandParameter="75"
                                            Content="75%"
                                            Margin="0,0,8,0" />
                                        <ui:Button
                                            Command="{Binding SetProgressCommand}"
                                            CommandParameter="100"
                                            Content="100%" />
                                    </StackPanel>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </Grid>

                    <!--  代码示例  -->
                    <codeExample:CodeExampleControl
                        CSharpCode="{Binding BasicProgressRingCSharpExample}"
                        Description="基础 ProgressRing 控件的使用方法"
                        IsExpanded="False"
                        ShowTabs="True"
                        Title="基础用法"
                        XamlCode="{Binding BasicProgressRingXamlExample}" />
                </StackPanel>
            </ui:CardExpander>

            <!--  高级功能演示  -->
            <ui:CardExpander
                Header="高级功能演示"
                Icon="{ui:SymbolIcon Settings24}"
                IsExpanded="True"
                Style="{StaticResource ZyloDemoCardStyle}">
                <StackPanel>

                    <!--  多个进度环展示  -->
                    <TextBlock
                        FontSize="16"
                        FontWeight="SemiBold"
                        Margin="0,0,0,16"
                        Text="不同大小和样式的进度环" />

                    <UniformGrid Columns="4" Rows="2">
                        <!--  小型进度环  -->
                        <StackPanel HorizontalAlignment="Center">
                            <ui:ProgressRing
                                Height="24"
                                IsIndeterminate="True"
                                Margin="0,0,0,8"
                                Width="24" />
                            <TextBlock HorizontalAlignment="Center" Text="小型 (24px)" />
                        </StackPanel>

                        <!--  中型进度环  -->
                        <StackPanel HorizontalAlignment="Center">
                            <ui:ProgressRing
                                Height="48"
                                IsIndeterminate="False"
                                Margin="0,0,0,8"
                                Progress="25"
                                Width="48" />
                            <TextBlock HorizontalAlignment="Center" Text="中型 (25%)" />
                        </StackPanel>

                        <!--  大型进度环  -->
                        <StackPanel HorizontalAlignment="Center">
                            <ui:ProgressRing
                                Height="72"
                                IsIndeterminate="False"
                                Margin="0,0,0,8"
                                Progress="50"
                                Width="72" />
                            <TextBlock HorizontalAlignment="Center" Text="大型 (50%)" />
                        </StackPanel>

                        <!--  超大进度环  -->
                        <StackPanel HorizontalAlignment="Center">
                            <ui:ProgressRing
                                Height="96"
                                IsIndeterminate="False"
                                Margin="0,0,0,8"
                                Progress="75"
                                Width="96" />
                            <TextBlock HorizontalAlignment="Center" Text="超大 (75%)" />
                        </StackPanel>
                    </UniformGrid>

                    <!--  代码示例  -->
                    <codeExample:CodeExampleControl
                        CSharpCode="{Binding AdvancedProgressRingCSharpExample}"
                        Description="高级 ProgressRing 功能，包括进度控制和定时器使用"
                        IsExpanded="False"
                        ShowTabs="True"
                        Title="高级用法"
                        XamlCode="{Binding AdvancedProgressRingXamlExample}" />
                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>
</UserControl>