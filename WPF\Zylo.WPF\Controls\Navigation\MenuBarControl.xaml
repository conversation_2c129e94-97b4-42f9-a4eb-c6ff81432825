<UserControl
    d:DesignHeight="40"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Zylo.WPF.Controls.Navigation.MenuBarControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  MenuBar 样式资源  -->
        <Style TargetType="Menu" x:Key="MenuBarStyle">
            <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="0,0,0,1" />
            <Setter Property="Padding" Value="8,4" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}" />
        </Style>

        <!--  MenuItem 顶级样式  -->
        <Style TargetType="MenuItem" x:Key="TopLevelMenuItemStyle">
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="2,0" />
            <Setter Property="FontWeight" Value="Normal" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="MenuItem">
                        <Grid>
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}"
                                x:Name="Border">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!--  图标  -->
                                    <ContentPresenter
                                        Content="{TemplateBinding Icon}"
                                        Grid.Column="0"
                                        Margin="0,0,8,0"
                                        VerticalAlignment="Center"
                                        x:Name="Icon" />

                                    <!--  文本  -->
                                    <ContentPresenter
                                        ContentSource="Header"
                                        Grid.Column="1"
                                        VerticalAlignment="Center"
                                        x:Name="HeaderHost" />

                                    <!--  子菜单箭头  -->
                                    <Path
                                        Data="M0,0 L4,4 L0,8 Z"
                                        Fill="{TemplateBinding Foreground}"
                                        Grid.Column="2"
                                        Margin="8,0,0,0"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed"
                                        x:Name="ArrowPath" />
                                </Grid>
                            </Border>

                            <!--  子菜单弹出窗口  -->
                            <Popup
                                AllowsTransparency="False"
                                Focusable="False"
                                IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                Placement="Bottom"
                                PopupAnimation="Fade"
                                x:Name="PART_Popup">
                                <Border
                                    Background="{DynamicResource ApplicationBackgroundBrush}"
                                    BorderBrush="{DynamicResource ControlStrokeColorOnAccentDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="0"
                                    Padding="4">
                                    <ScrollViewer Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                        <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Cycle" />
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <!--  鼠标悬停效果  -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                            </Trigger>

                            <!--  按下效果  -->
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{DynamicResource ControlFillColorTertiaryBrush}" />
                            </Trigger>

                            <!--  子菜单打开状态  -->
                            <Trigger Property="IsSubmenuOpen" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                            </Trigger>

                            <!--  有子菜单时显示箭头  -->
                            <Trigger Property="HasItems" Value="True">
                                <Setter Property="Visibility" TargetName="ArrowPath" Value="Visible" />
                            </Trigger>

                            <!--  禁用状态  -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  MenuItem 子级样式  -->
        <Style TargetType="MenuItem" x:Key="SubMenuItemStyle">
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="4,2" />
            <Setter Property="Margin" Value="2" />
            <Setter Property="MinWidth" Value="160" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="MenuItem">
                        <Grid>
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}"
                                x:Name="Border">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!--  图标  -->
                                    <ContentPresenter
                                        Content="{TemplateBinding Icon}"
                                        Grid.Column="0"
                                        Height="16"
                                        Margin="0,0,8,0"
                                        VerticalAlignment="Center"
                                        Width="16"
                                        x:Name="Icon" />

                                    <!--  文本  -->
                                    <ContentPresenter
                                        ContentSource="Header"
                                        Grid.Column="1"
                                        VerticalAlignment="Center"
                                        x:Name="HeaderHost" />

                                    <!--  快捷键  -->
                                    <TextBlock
                                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                        Grid.Column="2"
                                        Margin="16,0,0,0"
                                        Text="{TemplateBinding InputGestureText}"
                                        VerticalAlignment="Center"
                                        x:Name="InputGestureText" />

                                    <!--  子菜单箭头  -->
                                    <Path
                                        Data="M0,0 L4,4 L0,8 Z"
                                        Fill="{TemplateBinding Foreground}"
                                        Grid.Column="3"
                                        Margin="8,0,0,0"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed"
                                        x:Name="ArrowPath" />
                                </Grid>
                            </Border>

                            <!--  子菜单弹出窗口  -->
                            <Popup
                                AllowsTransparency="False"
                                Focusable="False"
                                IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                Placement="Right"
                                PopupAnimation="Fade"
                                x:Name="PART_Popup">
                                <Border
                                    Background="{DynamicResource ApplicationBackgroundBrush}"
                                    BorderBrush="{DynamicResource ControlStrokeColorOnAccentDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Padding="4">
                                    <ScrollViewer Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                        <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Cycle" />
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <!--  鼠标悬停效果  -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                            </Trigger>

                            <!--  按下效果  -->
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{DynamicResource ControlFillColorTertiaryBrush}" />
                            </Trigger>

                            <!--  有子菜单时显示箭头  -->
                            <Trigger Property="HasItems" Value="True">
                                <Setter Property="Visibility" TargetName="ArrowPath" Value="Visible" />
                            </Trigger>

                            <!--  禁用状态  -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!--  MenuBar 主体  -->
    <Border
        Background="{DynamicResource ApplicationBackgroundBrush}"
        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
        BorderThickness="0,0,0,1">
        <Menu
            HorizontalAlignment="Stretch"
            ItemsSource="{Binding MenuItems, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Style="{StaticResource MenuBarStyle}"
            VerticalAlignment="Center"
            x:Name="MainMenuBar">

            <!--  顶级菜单项模板  -->
            <Menu.ItemContainerStyle>
                <Style BasedOn="{StaticResource TopLevelMenuItemStyle}" TargetType="MenuItem">
                    <Setter Property="Header" Value="{Binding Header}" />
                    <Setter Property="Icon" Value="{Binding Icon}" />
                    <Setter Property="Command" Value="{Binding Path=MenuItemClickCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}" />
                    <Setter Property="CommandParameter" Value="{Binding CommandParameter}" />
                    <Setter Property="IsEnabled" Value="{Binding IsEnabled}" />
                    <Setter Property="ItemsSource" Value="{Binding Children}" />

                    <!--  子菜单项样式  -->
                    <Setter Property="ItemContainerStyle">
                        <Setter.Value>
                            <Style BasedOn="{StaticResource SubMenuItemStyle}" TargetType="MenuItem">
                                <Setter Property="Header" Value="{Binding Header}" />
                                <Setter Property="Icon" Value="{Binding Icon}" />
                                <Setter Property="Command" Value="{Binding Path=MenuItemClickCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}" />
                                <Setter Property="CommandParameter" Value="{Binding CommandParameter}" />
                                <Setter Property="IsEnabled" Value="{Binding IsEnabled}" />
                                <Setter Property="InputGestureText" Value="{Binding InputGestureText}" />
                                <Setter Property="ItemsSource" Value="{Binding Children}" />
                                <Style.Triggers>
                                    <!--  分隔线样式  -->
                                    <DataTrigger Binding="{Binding IsSeparator}" Value="True">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="MenuItem">
                                                    <Separator Margin="0,2" Style="{DynamicResource {x:Static MenuItem.SeparatorStyleKey}}" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Menu.ItemContainerStyle>
        </Menu>
    </Border>
</UserControl>
