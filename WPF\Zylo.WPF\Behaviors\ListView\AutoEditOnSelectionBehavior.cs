using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors.ListView;

/// <summary>
/// 自动编辑选中项行为 - 当ListBox选中项变化时自动触发编辑命令
/// </summary>
/// <remarks>
/// 使用场景：
/// - 用户点击列表项就直接进入编辑模式
/// - 无需额外点击"编辑"按钮
/// - 提供更流畅的用户体验
/// </remarks>
public class AutoEditOnSelectionBehavior : Behavior<ListBox>
{
    #region 编辑命令依赖属性

    /// <summary>
    /// 编辑命令依赖属性
    /// </summary>
    public static readonly DependencyProperty EditCommandProperty =
        DependencyProperty.Register(
            nameof(EditCommand),
            typeof(ICommand),
            typeof(AutoEditOnSelectionBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 编辑命令 - 当选中项变化时执行的命令
    /// </summary>
    public ICommand EditCommand
    {
        get => (ICommand)GetValue(EditCommandProperty);
        set => SetValue(EditCommandProperty, value);
    }

    #endregion

    #region 延迟执行属性

    /// <summary>
    /// 延迟执行时间（毫秒）依赖属性
    /// </summary>
    public static readonly DependencyProperty DelayMillisecondsProperty =
        DependencyProperty.Register(
            nameof(DelayMilliseconds),
            typeof(int),
            typeof(AutoEditOnSelectionBehavior),
            new PropertyMetadata(100)); // 默认延迟100毫秒

    /// <summary>
    /// 延迟执行时间（毫秒）- 避免快速切换选择时频繁触发编辑
    /// </summary>
    public int DelayMilliseconds
    {
        get => (int)GetValue(DelayMillisecondsProperty);
        set => SetValue(DelayMillisecondsProperty, value);
    }

    #endregion

    #region 启用状态属性

    /// <summary>
    /// 是否启用自动编辑依赖属性
    /// </summary>
    public static readonly DependencyProperty IsEnabledProperty =
        DependencyProperty.Register(
            nameof(IsEnabled),
            typeof(bool),
            typeof(AutoEditOnSelectionBehavior),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否启用自动编辑
    /// </summary>
    public bool IsEnabled
    {
        get => (bool)GetValue(IsEnabledProperty);
        set => SetValue(IsEnabledProperty, value);
    }

    #endregion

    #region 私有字段

    private System.Windows.Threading.DispatcherTimer? _delayTimer;
    private object? _pendingSelectedItem;

    #endregion

    #region 行为生命周期

    /// <summary>
    /// 附加到ListBox时
    /// </summary>
    protected override void OnAttached()
    {
        base.OnAttached();
        
        // 初始化延迟计时器
        _delayTimer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(DelayMilliseconds)
        };
        _delayTimer.Tick += OnDelayTimerTick;
        
        // 监听选择变化事件
        AssociatedObject.SelectionChanged += OnSelectionChanged;
    }

    /// <summary>
    /// 从ListBox分离时
    /// </summary>
    protected override void OnDetaching()
    {
        if (AssociatedObject != null)
        {
            AssociatedObject.SelectionChanged -= OnSelectionChanged;
        }
        
        // 清理计时器
        if (_delayTimer != null)
        {
            _delayTimer.Tick -= OnDelayTimerTick;
            _delayTimer.Stop();
            _delayTimer = null;
        }
        
        base.OnDetaching();
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 选择变化事件处理
    /// </summary>
    private void OnSelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        // 检查是否启用
        if (!IsEnabled) return;
        
        // 检查是否有新选中项
        if (e.AddedItems.Count == 0) return;
        
        var selectedItem = e.AddedItems[0];
        if (selectedItem == null) return;
        
        // 停止之前的计时器
        _delayTimer?.Stop();
        
        // 设置待处理的选中项
        _pendingSelectedItem = selectedItem;
        
        // 启动延迟计时器
        if (DelayMilliseconds > 0)
        {
            _delayTimer!.Interval = TimeSpan.FromMilliseconds(DelayMilliseconds);
            _delayTimer.Start();
        }
        else
        {
            // 立即执行
            ExecuteEditCommand(selectedItem);
        }
    }

    /// <summary>
    /// 延迟计时器触发事件
    /// </summary>
    private void OnDelayTimerTick(object? sender, EventArgs e)
    {
        _delayTimer?.Stop();
        
        if (_pendingSelectedItem != null)
        {
            ExecuteEditCommand(_pendingSelectedItem);
            _pendingSelectedItem = null;
        }
    }

    /// <summary>
    /// 执行编辑命令
    /// </summary>
    private void ExecuteEditCommand(object selectedItem)
    {
        if (EditCommand == null) return;
        
        // 检查命令是否可以执行
        if (EditCommand.CanExecute(selectedItem))
        {
            EditCommand.Execute(selectedItem);
        }
    }

    #endregion
}
