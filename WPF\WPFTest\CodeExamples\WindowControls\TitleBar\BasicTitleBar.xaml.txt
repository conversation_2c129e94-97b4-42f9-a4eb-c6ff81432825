<!-- 基础 TitleBar 控件示例 -->
<UserControl x:Class="WPFTest.Views.WindowControls.BasicTitleBarExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 基础 TitleBar -->
        <ui:TitleBar Grid.Row="0"
                    Title="我的应用程序"
                    Height="35"
                    ShowMinimize="True"
                    ShowMaximize="True"
                    ShowClose="True"
                    CanMaximize="True">
            
            <!-- 可选：自定义图标 -->
            <ui:TitleBar.Icon>
                <ui:SymbolIcon Symbol="Window24" FontSize="16"/>
            </ui:TitleBar.Icon>
        </ui:TitleBar>

        <!-- 窗口内容区域 -->
        <Border Grid.Row="1" 
                Background="{DynamicResource LayerFillColorDefaultBrush}"
                Padding="20">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="🪟 基础 TitleBar 示例" 
                          FontSize="20" 
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="这是一个基础的 TitleBar 控件演示" 
                          FontSize="14" 
                          Opacity="0.8"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,20"/>

                <!-- 功能说明 -->
                <ui:Card Padding="16" MaxWidth="400">
                    <StackPanel>
                        <TextBlock Text="TitleBar 功能特性：" FontWeight="Medium" Margin="0,0,0,8"/>
                        <TextBlock Text="• 自定义标题显示" FontSize="12" Margin="0,0,0,4"/>
                        <TextBlock Text="• 最小化/最大化/关闭按钮" FontSize="12" Margin="0,0,0,4"/>
                        <TextBlock Text="• 可配置按钮显示状态" FontSize="12" Margin="0,0,0,4"/>
                        <TextBlock Text="• 支持自定义图标" FontSize="12" Margin="0,0,0,4"/>
                        <TextBlock Text="• 现代化 WPF-UI 样式" FontSize="12" Margin="0,0,0,4"/>
                        <TextBlock Text="• 支持拖拽移动窗口" FontSize="12"/>
                    </StackPanel>
                </ui:Card>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
