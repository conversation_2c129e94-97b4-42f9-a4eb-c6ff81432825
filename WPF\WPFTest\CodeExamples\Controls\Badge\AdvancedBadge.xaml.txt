<!-- 高级 Badge 控件示例 -->
<UserControl
    x:Class="WPFTest.Examples.AdvancedBadgeExample"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        
        <!-- 自定义 Badge 样式 -->
        <Style x:Key="CustomBadgeStyle" TargetType="ui:Badge">
            <Setter Property="Margin" Value="4" />
            <Setter Property="Padding" Value="8,4" />
            <Setter Property="CornerRadius" Value="12" />
        </Style>
        
        <!-- 圆形 Badge 样式 -->
        <Style x:Key="CircleBadgeStyle" TargetType="ui:Badge">
            <Setter Property="Width" Value="24" />
            <Setter Property="Height" Value="24" />
            <Setter Property="CornerRadius" Value="12" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
        </Style>
        
        <!-- 大型 Badge 样式 -->
        <Style x:Key="LargeBadgeStyle" TargetType="ui:Badge">
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer>
        <StackPanel Margin="20" Spacing="24">

            <!-- 数据绑定 Badge -->
            <GroupBox Header="数据绑定 Badge">
                <StackPanel Spacing="16">
                    
                    <!-- 绑定到 ViewModel 属性 -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="绑定到 ViewModel:" FontWeight="Medium" />
                        <ui:Badge 
                            Appearance="{Binding BadgeAppearance}"
                            Visibility="{Binding ShowBadge, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="{Binding BadgeContent}" />
                        </ui:Badge>
                    </StackPanel>

                    <!-- 控制面板 -->
                    <GroupBox Header="控制面板">
                        <StackPanel Spacing="8">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <ui:Button 
                                    Content="更改内容" 
                                    Command="{Binding ChangeBadgeContentCommand}" />
                                <ui:Button 
                                    Content="更改外观" 
                                    Command="{Binding ChangeBadgeAppearanceCommand}" />
                                <ui:Button 
                                    Content="切换显示" 
                                    Command="{Binding ToggleBadgeVisibilityCommand}" />
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                </StackPanel>
            </GroupBox>

            <!-- 动态通知 Badge -->
            <GroupBox Header="动态通知 Badge">
                <StackPanel Spacing="16">
                    
                    <!-- 通知计数器 -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="通知计数器:" FontWeight="Medium" />
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <ui:Badge Appearance="Danger">
                                <ui:Button 
                                    Appearance="Primary" 
                                    Content="消息" 
                                    Icon="{ui:SymbolIcon Mail24}" />
                            </ui:Badge>
                            <TextBlock 
                                VerticalAlignment="Center"
                                Text="{Binding NotificationCount, StringFormat='计数: {0}'}" />
                        </StackPanel>
                    </StackPanel>

                    <!-- 通知控制 -->
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <ui:Button 
                            Content="+" 
                            Command="{Binding IncreaseNotificationCountCommand}"
                            Width="32" Height="32" />
                        <ui:Button 
                            Content="-" 
                            Command="{Binding DecreaseNotificationCountCommand}"
                            Width="32" Height="32" />
                        <ui:Button 
                            Content="重置" 
                            Command="{Binding ResetNotificationCountCommand}" />
                    </StackPanel>

                    <!-- 条件显示 Badge -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="条件显示 Badge:" FontWeight="Medium" />
                        <ui:Badge 
                            Appearance="Warning"
                            Visibility="{Binding HasNotifications, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="{Binding NotificationCount}" />
                        </ui:Badge>
                    </StackPanel>

                </StackPanel>
            </GroupBox>

            <!-- 自定义样式 Badge -->
            <GroupBox Header="自定义样式 Badge">
                <StackPanel Spacing="16">
                    
                    <!-- 使用自定义样式 -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="自定义样式:" FontWeight="Medium" />
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <ui:Badge 
                                Appearance="Primary" 
                                Style="{StaticResource CustomBadgeStyle}">
                                <TextBlock Text="自定义" />
                            </ui:Badge>
                            <ui:Badge 
                                Appearance="Success" 
                                Style="{StaticResource CircleBadgeStyle}">
                                <TextBlock Text="5" FontSize="10" />
                            </ui:Badge>
                            <ui:Badge 
                                Appearance="Info" 
                                Style="{StaticResource LargeBadgeStyle}">
                                <TextBlock Text="大型 Badge" />
                            </ui:Badge>
                        </StackPanel>
                    </StackPanel>

                    <!-- 动画 Badge -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="动画 Badge:" FontWeight="Medium" />
                        <ui:Badge Appearance="Danger">
                            <ui:Badge.Triggers>
                                <EventTrigger RoutedEvent="Loaded">
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation
                                                Storyboard.TargetProperty="Opacity"
                                                From="1.0" To="0.3" Duration="0:0:1"
                                                AutoReverse="True" />
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </ui:Badge.Triggers>
                            <TextBlock Text="闪烁" />
                        </ui:Badge>
                    </StackPanel>

                </StackPanel>
            </GroupBox>

            <!-- 复杂内容 Badge -->
            <GroupBox Header="复杂内容 Badge">
                <StackPanel Spacing="16">
                    
                    <!-- 多元素 Badge -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="多元素内容:" FontWeight="Medium" />
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            
                            <!-- 图标 + 文本 Badge -->
                            <ui:Badge Appearance="Success">
                                <StackPanel Orientation="Horizontal" Spacing="4">
                                    <ui:SymbolIcon Symbol="Checkmark24" FontSize="12" />
                                    <TextBlock Text="已完成" />
                                    <TextBlock Text="(5)" FontWeight="Bold" />
                                </StackPanel>
                            </ui:Badge>
                            
                            <!-- 进度 Badge -->
                            <ui:Badge Appearance="Info">
                                <StackPanel Spacing="2">
                                    <TextBlock Text="进度" FontSize="10" HorizontalAlignment="Center" />
                                    <ProgressBar 
                                        Width="40" Height="4" 
                                        Value="{Binding ProgressValue}" 
                                        Maximum="100" />
                                </StackPanel>
                            </ui:Badge>
                            
                            <!-- 多行文本 Badge -->
                            <ui:Badge Appearance="Warning">
                                <StackPanel>
                                    <TextBlock Text="多行" FontSize="10" HorizontalAlignment="Center" />
                                    <TextBlock Text="内容" FontSize="10" HorizontalAlignment="Center" />
                                </StackPanel>
                            </ui:Badge>
                            
                        </StackPanel>
                    </StackPanel>

                    <!-- 嵌套 Badge -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="嵌套 Badge:" FontWeight="Medium" />
                        <ui:Badge Appearance="Primary">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <TextBlock Text="主要内容" />
                                <ui:Badge Appearance="Danger">
                                    <TextBlock Text="子 Badge" FontSize="10" />
                                </ui:Badge>
                            </StackPanel>
                        </ui:Badge>
                    </StackPanel>

                    <!-- 交互式 Badge -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="交互式 Badge:" FontWeight="Medium" />
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            
                            <!-- 可点击 Badge -->
                            <ui:Badge 
                                Appearance="Primary" 
                                Cursor="Hand"
                                ToolTip="点击我">
                                <ui:Badge.InputBindings>
                                    <MouseBinding 
                                        MouseAction="LeftClick" 
                                        Command="{Binding BadgeClickCommand}" />
                                </ui:Badge.InputBindings>
                                <TextBlock Text="可点击" />
                            </ui:Badge>
                            
                            <!-- 可关闭 Badge -->
                            <ui:Badge Appearance="Secondary">
                                <StackPanel Orientation="Horizontal" Spacing="4">
                                    <TextBlock Text="可关闭" />
                                    <ui:Button 
                                        Content="×" 
                                        FontSize="10" 
                                        Width="16" Height="16"
                                        Padding="0"
                                        Command="{Binding CloseBadgeCommand}" />
                                </StackPanel>
                            </ui:Badge>
                            
                        </StackPanel>
                    </StackPanel>

                </StackPanel>
            </GroupBox>

            <!-- 实际应用场景 -->
            <GroupBox Header="实际应用场景">
                <StackPanel Spacing="16">
                    
                    <!-- 用户状态 -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="用户状态指示器:" FontWeight="Medium" />
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <Border 
                                Width="40" Height="40" 
                                CornerRadius="20"
                                Background="LightGray">
                                <TextBlock 
                                    Text="头像" 
                                    HorizontalAlignment="Center" 
                                    VerticalAlignment="Center"
                                    FontSize="10" />
                            </Border>
                            <ui:Badge 
                                Appearance="{Binding UserStatusAppearance}"
                                Style="{StaticResource CircleBadgeStyle}"
                                Margin="-16,-16,0,0">
                                <Ellipse 
                                    Width="8" Height="8" 
                                    Fill="White" />
                            </ui:Badge>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="用户名" FontWeight="Medium" />
                                <TextBlock Text="{Binding UserStatus}" FontSize="12" />
                            </StackPanel>
                        </StackPanel>
                        <ui:Button 
                            Content="更改状态" 
                            Command="{Binding ChangeUserStatusCommand}" />
                    </StackPanel>

                    <!-- 购物车 Badge -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="购物车示例:" FontWeight="Medium" />
                        <ui:Badge Appearance="Danger">
                            <ui:Button 
                                Appearance="Primary"
                                Content="购物车"
                                Icon="{ui:SymbolIcon Cart24}" />
                        </ui:Badge>
                    </StackPanel>

                    <!-- 标签云 -->
                    <StackPanel Spacing="8">
                        <TextBlock Text="标签云:" FontWeight="Medium" />
                        <WrapPanel>
                            <ui:Badge Appearance="Info" Margin="2">
                                <TextBlock Text="C#" />
                            </ui:Badge>
                            <ui:Badge Appearance="Success" Margin="2">
                                <TextBlock Text="WPF" />
                            </ui:Badge>
                            <ui:Badge Appearance="Warning" Margin="2">
                                <TextBlock Text="MVVM" />
                            </ui:Badge>
                            <ui:Badge Appearance="Primary" Margin="2">
                                <TextBlock Text="UI设计" />
                            </ui:Badge>
                            <ui:Badge Appearance="Secondary" Margin="2">
                                <TextBlock Text="开发" />
                            </ui:Badge>
                            <ui:Badge Appearance="Danger" Margin="2">
                                <TextBlock Text="测试" />
                            </ui:Badge>
                        </WrapPanel>
                    </StackPanel>

                </StackPanel>
            </GroupBox>

        </StackPanel>
    </ScrollViewer>
</UserControl>
