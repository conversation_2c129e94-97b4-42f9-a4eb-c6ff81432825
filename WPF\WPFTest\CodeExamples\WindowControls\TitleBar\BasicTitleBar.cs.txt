using System;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.ViewModels.WindowControls
{
    /// <summary>
    /// 基础 TitleBar 示例 ViewModel
    /// </summary>
    public partial class BasicTitleBarViewModel : ObservableObject
    {
        /// <summary>
        /// 标题栏标题
        /// </summary>
        [ObservableProperty]
        public partial string TitleBarTitle { get; set; } = "我的应用程序";

        /// <summary>
        /// 是否显示最小化按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowMinimize { get; set; } = true;

        /// <summary>
        /// 是否显示最大化按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowMaximize { get; set; } = true;

        /// <summary>
        /// 是否显示关闭按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowClose { get; set; } = true;

        /// <summary>
        /// 是否可以最大化
        /// </summary>
        [ObservableProperty]
        public partial bool CanMaximize { get; set; } = true;

        /// <summary>
        /// 标题栏高度
        /// </summary>
        [ObservableProperty]
        public partial double TitleBarHeight { get; set; } = 35;

        /// <summary>
        /// 当前状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "TitleBar 就绪";

        /// <summary>
        /// 构造函数
        /// </summary>
        public BasicTitleBarViewModel()
        {
            StatusMessage = "基础 TitleBar 控件已初始化";
        }

        /// <summary>
        /// 更新标题命令
        /// </summary>
        [RelayCommand]
        private void UpdateTitle()
        {
            try
            {
                TitleBarTitle = $"更新时间: {DateTime.Now:HH:mm:ss}";
                StatusMessage = "标题已更新";
            }
            catch (Exception ex)
            {
                StatusMessage = $"更新标题失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 切换按钮显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleButtons()
        {
            try
            {
                ShowMinimize = !ShowMinimize;
                ShowMaximize = !ShowMaximize;
                StatusMessage = $"按钮显示已切换 - 最小化: {ShowMinimize}, 最大化: {ShowMaximize}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"切换按钮失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 调整高度命令
        /// </summary>
        [RelayCommand]
        private void AdjustHeight()
        {
            try
            {
                // 在 25-50 之间循环调整高度
                TitleBarHeight = TitleBarHeight >= 50 ? 25 : TitleBarHeight + 5;
                StatusMessage = $"标题栏高度调整为: {TitleBarHeight}px";
            }
            catch (Exception ex)
            {
                StatusMessage = $"调整高度失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 重置设置命令
        /// </summary>
        [RelayCommand]
        private void ResetSettings()
        {
            try
            {
                TitleBarTitle = "我的应用程序";
                ShowMinimize = true;
                ShowMaximize = true;
                ShowClose = true;
                CanMaximize = true;
                TitleBarHeight = 35;
                StatusMessage = "设置已重置为默认值";
            }
            catch (Exception ex)
            {
                StatusMessage = $"重置设置失败: {ex.Message}";
            }
        }
    }
}
