<!-- 高级 MenuBar 使用示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 带图标的 MenuBar -->
    <GroupBox Header="带图标的 MenuBar" Padding="15">
        <StackPanel>
            <!-- 带图标的菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding AdvancedMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 高级菜单栏包含图标、多级子菜单和现代化样式" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 自定义样式 MenuBar -->
    <GroupBox Header="自定义样式 MenuBar" Padding="15">
        <StackPanel>
            <!-- 自定义样式的菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding AdvancedMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="45">
                <zylo:MenuBarControl.Resources>
                    <!-- 自定义顶级菜单项样式 -->
                    <Style TargetType="MenuItem" BasedOn="{StaticResource TopLevelMenuItemStyle}">
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                        <Setter Property="Padding" Value="16,10"/>
                    </Style>
                </zylo:MenuBarControl.Resources>
            </zylo:MenuBarControl>
            
            <TextBlock Text="💡 自定义样式的菜单栏，使用强调色和加粗字体" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                       Margin="0,10,0,0"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 多级子菜单 MenuBar -->
    <GroupBox Header="多级子菜单 MenuBar" Padding="15">
        <StackPanel>
            <!-- 多级子菜单的菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding MultiLevelMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 支持多级子菜单，可以创建复杂的菜单结构" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 动态菜单 MenuBar -->
    <GroupBox Header="动态菜单 MenuBar" Padding="15">
        <StackPanel>
            <!-- 控制按钮 -->
            <WrapPanel Margin="0,0,0,10" Orientation="Horizontal">
                <Button Content="➕ 添加菜单项"
                        Command="{Binding AddMenuItemCommand}"
                        Margin="0,0,8,0"/>
                <Button Content="🗑️ 清空菜单"
                        Command="{Binding ClearMenuItemsCommand}"
                        Margin="0,0,8,0"/>
            </WrapPanel>
            
            <!-- 动态菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding DynamicMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 动态菜单栏，可以在运行时添加或删除菜单项" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 主题适配 MenuBar -->
    <GroupBox Header="主题适配 MenuBar" Padding="15">
        <StackPanel>
            <!-- 主题适配的菜单栏 -->
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="4">
                <zylo:MenuBarControl MenuItems="{Binding AdvancedMenuItems}"
                                     MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                     IsMenuEnabled="True"
                                     MenuHeight="40"/>
            </Border>
            
            <TextBlock Text="💡 自动适配 WPF-UI 主题，支持明暗模式切换" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                       Margin="0,10,0,0"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 复选框菜单项 -->
    <GroupBox Header="复选框菜单项" Padding="15">
        <StackPanel>
            <!-- 带复选框的菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding CheckableMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 支持复选框菜单项，用于切换功能开关" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
</StackPanel>
