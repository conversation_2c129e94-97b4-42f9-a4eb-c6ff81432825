using System.Windows;
using AlphaPM.ViewModels;
using AlphaPM.Views;
using Prism.Ioc;
using Prism.Modularity;
using Zylo.WPF.Services;
using Zylo.WPF.YPrism;

namespace AlphaPM;

/// <summary>
/// 🚀 WPFTest 应用程序 Bootstrapper - 基于 Zylo.WPF 框架
/// </summary>
/// <remarks>
/// 继承自 ZyloBootstrapper，自动获得通用功能：
/// - CommunityToolkit.Mvvm 服务自动注册（IMessenger 等）
/// - 容器访问方法（ZyloContainer.Resolve）
/// - 通用生命周期管理
///
/// 同时保留完整的 Prism 功能配置：
/// - 依赖注入容器配置
/// - 区域管理和导航
/// - 模块化系统
/// - 事件聚合器
/// - 对话框服务
/// - ViewModelLocator 配置
/// - 异常处理
/// </remarks>
public class AppBootstrapper : ZyloBootstrapper
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<AppBootstrapper>();
    #region 核心生命周期方法

    /// <summary>
    /// 创建应用程序的主窗口 (Shell)
    /// </summary>
    /// <returns>主窗口实例</returns>
    /// <remarks>
    /// Shell 是应用程序的主容器，通常包含菜单、工具栏、状态栏和主要的内容区域。
    /// Prism 会自动为 Shell 配置 RegionManager 和 ViewModel 绑定。
    /// </remarks>
    protected override DependencyObject CreateShell()
    {
       
        // 强制所有类输出Debug级别及以上（开发调试）
        YLogger.ForceDebugMode();
        //
        // _logger.Debug("🚀Debug");
        // _logger.InfoDetailed("详细信息");
        // _logger.Info("一般信息");
        // _logger.InfoSimple("简化信息");
        // _logger.Warning("警告信息");
        // _logger.Error("错误信息");
    
        
        
       _logger.Info("🏠 正在创建 WPFTest 应用程序 Shell...");
        var shell = ZyloContainer.Resolve<MainView>();
       _logger.Info("✅ WPFTest Shell 创建完成");
        return shell;
    }

    /// <summary>
    /// 注册应用程序类型到依赖注入容器
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 在这里注册：
    /// - ViewModels（如果需要手动注册）
    /// - 服务接口和实现
    /// - 导航页面（利用 Prism 自动 View-ViewModel 绑定）
    /// - 对话框
    ///
    /// 注意：IMessenger 等 CommunityToolkit.Mvvm 服务已由 ZyloBootstrapper 自动注册。
    /// </remarks>
    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
       _logger.Debug("📝 正在注册 WPFTest 应用程序类型...");

        // 🏠 注册主窗口和 ViewModel
        // containerRegistry.Register<Views.MainView>();
        // containerRegistry.Register<ViewModels.MainViewModel>();

        // 注册 MainViewModel 作为 IConfigureService 的实现
        containerRegistry.Register<IConfigureService, AlphaPM.ViewModels.MainViewModel>();

        
        containerRegistry.RegisterForNavigation<AlphaPM.Views.MainView, AlphaPM.ViewModels.MainViewModel>();
       _logger.Debug("✅ MainView 和 MainViewModel 注册完成");






       _logger.Debug("✅ 导航页面注册完成");

        // 🔧 注册应用程序服务
        containerRegistry.RegisterSingleton<IThemeManagementService, ThemeManagementService>(); // 主题管理服务
       _logger.Debug("✅ 应用程序服务注册完成");

     

       _logger.Debug("✅ WPFTest 类型注册完成");
    }

    /// <summary>
    /// 注册 Prism 框架必需的类型
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 这个方法由 Prism 框架调用，用于注册核心服务：
    /// - IEventAggregator: 事件聚合器
    /// - IRegionManager: 区域管理器
    /// - IDialogService: 对话框服务
    /// - IModuleManager: 模块管理器
    /// - IMessenger: 消息服务（由 ZyloBootstrapper 自动添加）
    /// - 等等...
    /// </remarks>
    protected override void RegisterRequiredTypes(IContainerRegistry containerRegistry)
    {
       _logger.Debug("🔧 正在注册 Prism 框架必需类型...");
        base.RegisterRequiredTypes(containerRegistry);
       _logger.Debug("✅ Prism 框架必需类型注册完成");
    }

    /// <summary>
    /// 注册框架异常类型
    /// </summary>
    /// <remarks>
    /// 注册不被视为根异常的异常类型，用于异常处理和调试。
    /// 这些异常通常是框架内部异常，不应该暴露给最终用户。
    /// </remarks>
    protected override void RegisterFrameworkExceptionTypes()
    {
       _logger.Debug("⚠️ 正在注册框架异常类型...");
        base.RegisterFrameworkExceptionTypes();

        // 可以添加自定义异常类型
        // ExceptionExtensions.RegisterFrameworkExceptionType(typeof(CustomFrameworkException));

       _logger.Debug("✅ 框架异常类型注册完成");
    }

    /// <summary>
    /// 配置 ViewModelLocator
    /// </summary>
    /// <remarks>
    /// 配置 Prism 的 ViewModelLocator，用于自动 View-ViewModel 绑定：
    /// - 设置 ViewModel 工厂
    /// - 配置命名约定
    /// - 设置 ViewModel 解析策略
    /// </remarks>
    protected override void ConfigureViewModelLocator()
    {
       _logger.Debug("🎯 正在配置 ViewModelLocator...");
        base.ConfigureViewModelLocator();

        // 可以自定义 ViewModelLocator 配置
        // ViewModelLocationProvider.SetDefaultViewModelFactory((view, type) => ZyloContainer.Resolve(type));

       _logger.Debug("✅ ViewModelLocator 配置完成");
    }

    /// <summary>
    /// 注册额外的 CommunityToolkit.Mvvm 服务
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 在这里可以注册项目特定的 CommunityToolkit.Mvvm 相关服务
    /// </remarks>
    protected override void RegisterAdditionalCommunityToolkitServices(IContainerRegistry containerRegistry)
    {
       _logger.Debug("🔧 正在注册额外的 CommunityToolkit.Mvvm 服务...");

        // 注册项目特定的服务
        // containerRegistry.RegisterSingleton<IFileService, FileService>();
        // containerRegistry.RegisterSingleton<IDialogService, CustomDialogService>();
        // containerRegistry.RegisterSingleton<IThemeService, ThemeService>();

       _logger.Debug("✅ 额外的 CommunityToolkit.Mvvm 服务注册完成");
    }

    #endregion

    #region 模块化系统配置

    /// <summary>
    /// 配置模块目录
    /// </summary>
    /// <param name="moduleCatalog">模块目录</param>
    /// <remarks>
    /// 在这里配置应用程序的模块：
    /// - 添加模块引用
    /// - 设置模块依赖关系
    /// - 配置模块加载策略（按需加载、延迟加载等）
    /// </remarks>
    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
       _logger.Debug("📦 正在配置模块目录...");

        // 添加模块（示例）
        // moduleCatalog.AddModule<UserModule>();
        // moduleCatalog.AddModule<OrderModule>(InitializationMode.OnDemand);

        // 从目录加载模块
        // var directoryModuleCatalog = new DirectoryModuleCatalog() { ModulePath = @".\Modules" };
        // moduleCatalog.AddCatalog(directoryModuleCatalog);

       _logger.Debug("✅ 模块目录配置完成");
    }

    /// <summary>
    /// 初始化模块
    /// </summary>
    /// <remarks>
    /// 这个方法在所有配置完成后被调用，用于初始化已注册的模块。
    /// </remarks>
    protected override void InitializeModules()
    {
       _logger.Debug("🔄 正在初始化模块...");
        base.InitializeModules();
       _logger.Debug("✅ 模块初始化完成");
    }

    #endregion

    #region 区域管理配置

    /// <summary>
    /// 配置区域适配器映射
    /// </summary>
    /// <param name="regionAdapterMappings">区域适配器映射</param>
    /// <remarks>
    /// 区域适配器负责将 WPF 控件转换为 Prism 区域：
    /// - ContentControl -> ContentControlRegionAdapter
    /// - ItemsControl -> ItemsControlRegionAdapter
    /// - Selector -> SelectorRegionAdapter
    /// - TabControl -> TabControlRegionAdapter (自定义)
    /// </remarks>
    protected override void ConfigureRegionAdapterMappings(RegionAdapterMappings regionAdapterMappings)
    {
       _logger.Debug("🗺️ 正在配置区域适配器映射...");

        base.ConfigureRegionAdapterMappings(regionAdapterMappings);

        // 添加自定义区域适配器
        // regionAdapterMappings.RegisterMapping(typeof(StackPanel), ZyloContainer.Resolve<StackPanelRegionAdapter>());
        // regionAdapterMappings.RegisterMapping(typeof(Grid), ZyloContainer.Resolve<GridRegionAdapter>());

       _logger.Debug("✅ 区域适配器映射配置完成");
    }

    /// <summary>
    /// 配置默认区域行为
    /// </summary>
    /// <param name="regionBehaviors">区域行为工厂</param>
    /// <remarks>
    /// 区域行为定义了区域的默认功能：
    /// - AutoPopulateRegionBehavior: 自动填充区域
    /// - BindRegionContextToDependencyObjectBehavior: 绑定区域上下文
    /// - RegionActiveAwareBehavior: 区域激活感知
    /// - SyncRegionContextWithHostBehavior: 同步区域上下文
    /// - RegionManagerRegistrationBehavior: 区域管理器注册
    /// </remarks>
    protected override void ConfigureDefaultRegionBehaviors(IRegionBehaviorFactory regionBehaviors)
    {
       _logger.Debug("⚙️ 正在配置默认区域行为...");

        base.ConfigureDefaultRegionBehaviors(regionBehaviors);

        // 添加自定义区域行为
        // regionBehaviors.AddIfMissing("CustomBehavior", typeof(CustomRegionBehavior));

       _logger.Debug("✅ 默认区域行为配置完成");
    }

    #endregion

    #region Shell 初始化

    /// <summary>
    /// 初始化 Shell
    /// </summary>
    /// <param name="shell">Shell 实例</param>
    /// <remarks>
    /// 在这里执行 Shell 的最终初始化：
    /// - 显示主窗口
    /// - 设置窗口属性
    /// - 执行初始导航
    /// - 加载用户设置
    /// </remarks>
    protected override void InitializeShell(DependencyObject shell)
    {
       _logger.Debug("🚀 正在初始化 Shell...");

        if (shell is Window window)
        {
            // 设置窗口属性
            window.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // 显示主窗口
            window.Show();

            // 执行初始导航（可选）
            // var regionManager = ZyloContainer.Resolve<IRegionManager>();
            // regionManager.RequestNavigate("ContentRegion", "Page1View");
        }

       _logger.Debug("✅ Shell 初始化完成");
    }

    /// <summary>
    /// 应用程序初始化完成回调
    /// </summary>
    /// <remarks>
    /// 这是 Prism 初始化流程的最后一步，在这里可以：
    /// - 执行应用程序启动逻辑
    /// - 加载用户数据
    /// - 显示启动画面
    /// - 检查更新
    /// </remarks>
    protected override void OnInitialized()
    {
        base.OnInitialized();

       _logger.Debug("🎉 Prism 应用程序初始化完成！");
       _logger.Debug("📱 应用程序已准备就绪，可以开始使用所有 Prism 功能：");
       _logger.Debug("   ✅ 依赖注入容器");
       _logger.Debug("   ✅ 区域管理和导航");
       _logger.Debug("   ✅ 事件聚合器");
       _logger.Debug("   ✅ 对话框服务");
       _logger.Debug("   ✅ 模块化系统");
       _logger.Debug("   ✅ ViewModelLocator");
       _logger.Debug("   ✅ CommunityToolkit.Mvvm 服务");


        // 🎨 加载保存的主题设置
        var themeService = ZyloContainer.Resolve<IThemeManagementService>();
        themeService.LoadSavedThemeSettings();

        // 🔥 预热 Snackbar 服务，避免第一次使用延迟
        try
        {
            var snackbarService = ZyloContainer.Resolve<Zylo.WPF.Services.ISnackbarService>();
            _logger.Debug("✅ Snackbar 服务预热完成");
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ Snackbar 服务预热失败: {ex.Message}");
        }

        // 🚀 执行配置服务的初始化（包括默认导航）
        var configureService = ZyloContainer.Resolve<IConfigureService>();
        configureService.Configure();
    }



    #endregion
}
