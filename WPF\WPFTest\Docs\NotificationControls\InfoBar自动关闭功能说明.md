# InfoBar 自动关闭功能说明

## 🎯 功能概述

InfoBar 自动关闭功能允许用户设置 InfoBar 在显示一定时间后自动关闭，提供了类似 Toast 通知的用户体验。这个功能特别适用于临时性的信息提示，避免用户界面被过多的通知信息占据。

## ✨ 核心特性

### 1. **可配置的自动关闭**
- ✅ 支持启用/禁用自动关闭功能
- ✅ 可设置 1-60 秒的延迟时间
- ✅ 实时倒计时显示
- ✅ 多个 InfoBar 独立控制

### 2. **智能定时器管理**
- ✅ 每个 InfoBar 独立的定时器
- ✅ 自动清理过期定时器
- ✅ 支持手动停止定时器
- ✅ 内存安全的定时器处理

### 3. **用户友好的界面**
- ✅ 实时倒计时显示："将在 X 秒后自动关闭"
- ✅ 可视化的设置面板
- ✅ NumberBox 控件设置延迟时间
- ✅ CheckBox 控制启用状态

## 🔧 技术实现

### **核心组件**

#### 1. **定时器管理**
```csharp
// 自动关闭定时器字典
private readonly Dictionary<string, Timer> _autoCloseTimers = new();

/// <summary>
/// 启动自动关闭定时器
/// </summary>
private void StartAutoClose(string timerKey, Action closeAction)
{
    StopAutoCloseTimer(timerKey);
    var remainingSeconds = AutoCloseSeconds;
    
    var timer = new Timer(_ =>
    {
        remainingSeconds--;
        App.Current.Dispatcher.Invoke(() =>
        {
            if (remainingSeconds > 0)
            {
                AutoCloseCountdown = $"将在 {remainingSeconds} 秒后自动关闭";
            }
            else
            {
                AutoCloseCountdown = string.Empty;
                closeAction?.Invoke();
                StopAutoCloseTimer(timerKey);
            }
        });
    }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    
    _autoCloseTimers[timerKey] = timer;
}
```

#### 2. **属性绑定**
```csharp
[ObservableProperty]
private bool enableAutoClose = true;

[ObservableProperty]
private int autoCloseSeconds = 5;

[ObservableProperty]
private string autoCloseCountdown = string.Empty;
```

#### 3. **命令集成**
```csharp
[RelayCommand]
private void ShowInfoBar()
{
    IsInfoBarOpen = true;
    
    // 启动自动关闭
    if (EnableAutoClose)
    {
        StartAutoClose("InfoBar", () => IsInfoBarOpen = false);
    }
}
```

### **XAML 界面**

#### 1. **设置面板**
```xml
<StackPanel Orientation="Horizontal">
    <CheckBox Content="启用自动关闭"
              IsChecked="{Binding EnableAutoClose}"/>
    
    <TextBlock Text="延迟时间："/>
    <ui:NumberBox Value="{Binding AutoCloseSeconds, Mode=TwoWay}"
                  Minimum="1" Maximum="60" Width="80"/>
    <TextBlock Text="秒"/>
    
    <TextBlock Text="{Binding AutoCloseCountdown}"
               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
</StackPanel>
```

## 🚀 使用方式

### **基本使用**

1. **启用自动关闭**：勾选"启用自动关闭"复选框
2. **设置延迟时间**：在 NumberBox 中输入 1-60 秒的数值
3. **显示 InfoBar**：点击任意显示按钮
4. **观察倒计时**：界面会显示"将在 X 秒后自动关闭"
5. **自动关闭**：倒计时结束后 InfoBar 自动隐藏

### **高级功能**

#### **手动停止自动关闭**
- 点击"隐藏所有"按钮会立即停止所有定时器
- 手动关闭 InfoBar 会自动停止对应的定时器

#### **多 InfoBar 管理**
- 每个 InfoBar 都有独立的定时器
- 可以同时显示多个 InfoBar，各自独立倒计时
- 定时器使用唯一标识符管理，避免冲突

## 💡 设计亮点

### 1. **内存安全**
- 使用 `Dictionary<string, Timer>` 管理定时器
- 自动清理过期的定时器资源
- 防止内存泄漏

### 2. **线程安全**
- 使用 `App.Current.Dispatcher.Invoke()` 确保 UI 更新在主线程
- 定时器回调正确处理跨线程操作

### 3. **用户体验**
- 实时倒计时反馈
- 可配置的延迟时间
- 直观的控制界面

### 4. **扩展性**
- 支持不同类型的 InfoBar
- 可以轻松添加更多自动关闭选项
- 模块化的定时器管理

## 🔍 调试功能

### **日志记录**
```csharp
_logger.Info($"⏰ 启动 {timerKey} 自动关闭定时器，{AutoCloseSeconds} 秒后关闭");
_logger.Info($"⏰ {timerKey} 自动关闭");
```

### **状态跟踪**
- 实时显示当前状态
- 记录最后操作时间
- 倒计时文本实时更新

## 🎨 界面效果

### **设置面板**
```
⏰ 自动关闭设置：
☑ 启用自动关闭    延迟时间：[5] 秒    将在 3 秒后自动关闭
```

### **InfoBar 显示**
```
ℹ️ 信息提示
这是一个信息类型的 InfoBar，用于显示一般性信息。
                                                    [×]
```

## 🔧 自定义选项

### **延迟时间范围**
- 最小值：1 秒
- 最大值：60 秒
- 默认值：5 秒

### **支持的 InfoBar 类型**
- ✅ 信息类型 (Informational)
- ✅ 成功类型 (Success)
- ✅ 警告类型 (Warning)
- ✅ 错误类型 (Error)
- ✅ 动态类型 (Dynamic)

## 🚀 扩展建议

### **功能增强**
1. **不同类型不同延迟**：错误信息显示更长时间
2. **鼠标悬停暂停**：鼠标悬停时暂停倒计时
3. **声音提示**：倒计时结束时播放提示音
4. **动画效果**：添加淡出动画效果

### **配置持久化**
1. **保存用户设置**：记住用户的自动关闭偏好
2. **配置文件**：支持从配置文件加载设置
3. **主题适配**：根据主题调整倒计时文本颜色

---

## 📝 总结

InfoBar 自动关闭功能提供了完整的定时关闭解决方案，具有以下优势：

- ✅ **易于使用**：直观的设置界面
- ✅ **功能完整**：支持多种配置选项
- ✅ **性能优秀**：高效的定时器管理
- ✅ **扩展性强**：模块化设计便于扩展
- ✅ **用户友好**：实时反馈和状态显示

这个功能展示了如何在 WPF 应用中实现专业级的通知管理系统，为用户提供了类似现代操作系统通知的体验。
