using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Examples
{
    /// <summary>
    /// 高级 ProgressRing 示例 - 演示高级功能和自定义样式
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class AdvancedProgressRingExample : UserControl
    {
        public AdvancedProgressRingExample()
        {
            InitializeComponent();
            DataContext = new AdvancedProgressRingViewModel();
        }
    }

    /// <summary>
    /// 高级 ProgressRing ViewModel
    /// </summary>
    public partial class AdvancedProgressRingViewModel : ObservableObject
    {
        private readonly DispatcherTimer _simulationTimer;
        private readonly DispatcherTimer _statusTimer;

        #region 属性 - 使用 Partial Properties 语法

        /// <summary>
        /// 是否为不确定进度模式
        /// </summary>
        [ObservableProperty]
        public partial bool IsIndeterminate { get; set; } = false;

        /// <summary>
        /// 当前进度值
        /// </summary>
        [ObservableProperty]
        public partial double ProgressValue { get; set; } = 0;

        /// <summary>
        /// 最大进度值
        /// </summary>
        [ObservableProperty]
        public partial double MaximumValue { get; set; } = 100;

        /// <summary>
        /// 进度环大小
        /// </summary>
        [ObservableProperty]
        public partial double RingSize { get; set; } = 64;

        /// <summary>
        /// 进度环厚度
        /// </summary>
        [ObservableProperty]
        public partial double StrokeThickness { get; set; } = 4;

        /// <summary>
        /// 是否显示进度文本
        /// </summary>
        [ObservableProperty]
        public partial bool ShowProgressText { get; set; } = true;

        /// <summary>
        /// 进度文本
        /// </summary>
        [ObservableProperty]
        public partial string ProgressText { get; set; } = "0%";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "准备开始进度演示";

        /// <summary>
        /// 当前选择的样式
        /// </summary>
        [ObservableProperty]
        public partial string SelectedStyle { get; set; } = "默认";

        /// <summary>
        /// 当前选择的进度环样式
        /// </summary>
        [ObservableProperty]
        public partial Style? SelectedProgressRingStyle { get; set; }

        /// <summary>
        /// 是否正在运行模拟
        /// </summary>
        [ObservableProperty]
        public partial bool IsSimulationRunning { get; set; } = false;

        /// <summary>
        /// 模拟速度 (毫秒)
        /// </summary>
        [ObservableProperty]
        public partial int SimulationSpeed { get; set; } = 100;

        #endregion

        #region 集合属性

        /// <summary>
        /// 样式选项
        /// </summary>
        public ObservableCollection<string> StyleOptions { get; } = new()
        {
            "默认", "成功", "警告", "错误", "自定义"
        };

        #endregion

        #region 构造函数

        public AdvancedProgressRingViewModel()
        {
            // 初始化模拟定时器
            _simulationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(SimulationSpeed)
            };
            _simulationTimer.Tick += SimulationTimer_Tick;

            // 初始化状态定时器
            _statusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _statusTimer.Tick += StatusTimer_Tick;

            // 监听属性变化
            PropertyChanged += OnPropertyChanged;

            // 初始化进度文本
            UpdateProgressText();
        }

        #endregion

        #region 属性变化处理

        private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(ProgressValue):
                    UpdateProgressText();
                    break;
                case nameof(IsIndeterminate):
                    UpdateProgressText();
                    if (IsIndeterminate)
                    {
                        StatusMessage = "切换到不确定进度模式";
                    }
                    else
                    {
                        StatusMessage = "切换到确定进度模式";
                    }
                    break;
                case nameof(SelectedStyle):
                    UpdateProgressRingStyle();
                    break;
                case nameof(SimulationSpeed):
                    UpdateTimerInterval();
                    break;
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 开始模拟命令
        /// </summary>
        [RelayCommand]
        private void StartSimulation()
        {
            if (IsSimulationRunning) return;

            IsSimulationRunning = true;
            IsIndeterminate = false;
            ProgressValue = 0;
            StatusMessage = "开始进度模拟";
            _simulationTimer.Start();
            _statusTimer.Start();
        }

        /// <summary>
        /// 停止模拟命令
        /// </summary>
        [RelayCommand]
        private void StopSimulation()
        {
            if (!IsSimulationRunning) return;

            IsSimulationRunning = false;
            _simulationTimer.Stop();
            _statusTimer.Stop();
            StatusMessage = "进度模拟已停止";
        }

        /// <summary>
        /// 重置命令
        /// </summary>
        [RelayCommand]
        private void Reset()
        {
            StopSimulation();
            ProgressValue = 0;
            IsIndeterminate = false;
            StatusMessage = "进度已重置";
        }

        /// <summary>
        /// 设置进度命令
        /// </summary>
        [RelayCommand]
        private void SetProgress(string? value)
        {
            if (string.IsNullOrEmpty(value) || !double.TryParse(value, out double progress))
                return;

            StopSimulation();
            IsIndeterminate = false;
            ProgressValue = Math.Max(0, Math.Min(MaximumValue, progress));
            StatusMessage = $"手动设置进度为 {ProgressValue:F0}%";
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 模拟定时器事件
        /// </summary>
        private void SimulationTimer_Tick(object? sender, EventArgs e)
        {
            if (ProgressValue >= MaximumValue)
            {
                StopSimulation();
                StatusMessage = "进度模拟完成！";
                return;
            }

            ProgressValue += 1;
        }

        /// <summary>
        /// 状态定时器事件
        /// </summary>
        private void StatusTimer_Tick(object? sender, EventArgs e)
        {
            if (IsSimulationRunning)
            {
                var elapsed = DateTime.Now.Second % 4;
                var dots = new string('.', elapsed + 1);
                StatusMessage = $"正在进行进度模拟{dots}";
            }
        }

        /// <summary>
        /// 更新进度文本
        /// </summary>
        private void UpdateProgressText()
        {
            if (IsIndeterminate)
            {
                ProgressText = "加载中...";
            }
            else
            {
                var percentage = (ProgressValue / MaximumValue) * 100;
                ProgressText = $"{percentage:F0}%";
            }
        }

        /// <summary>
        /// 更新进度环样式
        /// </summary>
        private void UpdateProgressRingStyle()
        {
            // 这里可以根据选择的样式设置不同的 Style
            // 在实际应用中，可以从资源字典中获取对应的样式
            StatusMessage = $"样式已切换为: {SelectedStyle}";
        }

        /// <summary>
        /// 更新定时器间隔
        /// </summary>
        private void UpdateTimerInterval()
        {
            _simulationTimer.Interval = TimeSpan.FromMilliseconds(SimulationSpeed);
        }

        #endregion
    }

    /// <summary>
    /// 高级 ProgressRing 使用示例和工具类
    /// </summary>
    public static class ProgressRingAdvancedUsage
    {
        /// <summary>
        /// 创建带动画的进度更新
        /// </summary>
        public static void AnimateProgress(ProgressRing ring, double targetValue, TimeSpan duration)
        {
            if (ring == null) return;

            var startValue = ring.Value;
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(16) // ~60 FPS
            };

            var startTime = DateTime.Now;
            timer.Tick += (s, e) =>
            {
                var elapsed = DateTime.Now - startTime;
                var progress = Math.Min(1.0, elapsed.TotalMilliseconds / duration.TotalMilliseconds);
                
                // 使用缓动函数
                var easedProgress = EaseInOutQuad(progress);
                ring.Value = startValue + (targetValue - startValue) * easedProgress;

                if (progress >= 1.0)
                {
                    timer.Stop();
                    ring.Value = targetValue;
                }
            };

            ring.IsIndeterminate = false;
            timer.Start();
        }

        /// <summary>
        /// 缓动函数 - 二次方缓入缓出
        /// </summary>
        private static double EaseInOutQuad(double t)
        {
            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        }

        /// <summary>
        /// 创建文件上传进度环
        /// </summary>
        public static ProgressRing CreateFileUploadRing(string fileName)
        {
            var ring = new ProgressRing
            {
                IsIndeterminate = true,
                Width = 32,
                Height = 32
            };

            // 可以添加工具提示显示文件信息
            ring.ToolTip = $"上传文件: {fileName}";

            return ring;
        }

        /// <summary>
        /// 切换文件上传进度状态
        /// </summary>
        public static void ToggleFileUploadProgress(ProgressRing ring, bool isUploading)
        {
            if (ring != null)
            {
                ring.IsIndeterminate = isUploading;
                ring.ToolTip = isUploading ? "正在上传..." : "上传完成";
            }
        }

        /// <summary>
        /// 创建数据加载进度环
        /// </summary>
        public static ProgressRing CreateDataLoadingRing()
        {
            return new ProgressRing
            {
                IsIndeterminate = true,
                Width = 24,
                Height = 24,
                ToolTip = "正在加载数据..."
            };
        }
    }
}
