<!-- 基础 ProgressRing 示例 -->
<UserControl x:Class="WPFTest.Examples.BasicProgressRingExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <StackPanel Margin="20" Spacing="16">
        
        <!-- 标题 -->
        <TextBlock Text="基础 ProgressRing 示例" 
                   FontSize="18" 
                   FontWeight="SemiBold" />
        
        <!-- 不确定进度环 -->
        <GroupBox Header="不确定进度环">
            <StackPanel Orientation="Horizontal" Spacing="16">
                
                <!-- 小型 -->
                <StackPanel HorizontalAlignment="Center" Spacing="8">
                    <ui:ProgressRing IsIndeterminate="True" 
                                    Width="24" 
                                    Height="24" />
                    <TextBlock Text="小型 (24px)" 
                              HorizontalAlignment="Center" 
                              FontSize="12" />
                </StackPanel>
                
                <!-- 中型 -->
                <StackPanel HorizontalAlignment="Center" Spacing="8">
                    <ui:ProgressRing IsIndeterminate="True" 
                                    Width="48" 
                                    Height="48" />
                    <TextBlock Text="中型 (48px)" 
                              HorizontalAlignment="Center" 
                              FontSize="12" />
                </StackPanel>
                
                <!-- 大型 -->
                <StackPanel HorizontalAlignment="Center" Spacing="8">
                    <ui:ProgressRing IsIndeterminate="True" 
                                    Width="72" 
                                    Height="72" />
                    <TextBlock Text="大型 (72px)" 
                              HorizontalAlignment="Center" 
                              FontSize="12" />
                </StackPanel>
                
            </StackPanel>
        </GroupBox>
        
        <!-- 确定进度环 -->
        <GroupBox Header="确定进度环">
            <StackPanel Orientation="Horizontal" Spacing="16">
                
                <!-- 50% 进度环 -->
                <StackPanel HorizontalAlignment="Center" Spacing="8">
                    <ui:ProgressRing IsIndeterminate="False"
                                    Width="48"
                                    Height="48"
                                    Progress="50" />
                    <TextBlock Text="50% 进度"
                              HorizontalAlignment="Center"
                              FontSize="12" />
                </StackPanel>

                <!-- 25% 进度环 -->
                <StackPanel HorizontalAlignment="Center" Spacing="8">
                    <ui:ProgressRing IsIndeterminate="False"
                                    Width="32"
                                    Height="32"
                                    Progress="25" />
                    <TextBlock Text="25% 进度"
                              HorizontalAlignment="Center"
                              FontSize="12" />
                </StackPanel>

                <!-- 75% 进度环 -->
                <StackPanel HorizontalAlignment="Center" Spacing="8">
                    <ui:ProgressRing IsIndeterminate="False"
                                    Width="64"
                                    Height="64"
                                    Progress="75" />
                    <TextBlock Text="75% 进度"
                              HorizontalAlignment="Center"
                              FontSize="12" />
                </StackPanel>
                
            </StackPanel>
        </GroupBox>
        
        <!-- 基础用法说明 -->
        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="16">
            <StackPanel Spacing="8">
                <TextBlock Text="基础用法要点：" 
                          FontWeight="SemiBold" />
                <TextBlock Text="• IsIndeterminate=True 显示旋转动画，用于未知进度" />
                <TextBlock Text="• IsIndeterminate=False 显示具体进度，需设置 Value 和 Maximum" />
                <TextBlock Text="• Width 和 Height 控制进度环大小" />
                <TextBlock Text="• 默认样式会自动适应当前主题" />
            </StackPanel>
        </Border>
        
    </StackPanel>
</UserControl>
