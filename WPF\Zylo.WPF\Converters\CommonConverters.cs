using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Zylo.WPF.Converters;

/// <summary>
/// 通用数据转换器集合 - 符合MVVM模式的数据转换
/// </summary>
/// <remarks>
/// 🎯 设计目的：
/// - 提供可重用的数据转换器
/// - 分离View和ViewModel之间的数据转换逻辑
/// - 支持双向数据绑定
/// - 遵循单一职责原则
/// 
/// 💡 使用方式：
/// 在XAML中引用命名空间并使用转换器：
/// ```xml
/// <Window.Resources>
///     <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
/// </Window.Resources>
/// 
/// <TextBlock Foreground="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}}"/>
/// ```
/// </remarks>

/// <summary>
/// 布尔值到颜色转换器
/// </summary>
/// <remarks>
/// 功能：将布尔值转换为对应的颜色
/// - true → Green (启用状态)
/// - false → Red (禁用状态)
/// - null/其他 → Gray (未知状态)
/// 
/// 使用场景：状态指示器、文本颜色等
/// </remarks>
public class BoolToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isEnabled)
        {
            return isEnabled ? Colors.Green : Colors.Red;
        }
        return Colors.Gray;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException("BoolToColorConverter不支持反向转换");
    }
}

/// <summary>
/// 验证错误到字符串转换器
/// </summary>
/// <remarks>
/// 功能：将验证错误集合转换为可显示的字符串
/// - 将多个验证错误用换行符连接
/// - 空集合返回空字符串
/// 
/// 使用场景：显示表单验证错误信息
/// </remarks>
public class ValidationErrorsToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is System.Collections.IEnumerable errors)
        {
            var errorMessages = errors.Cast<object>().Select(e => e.ToString());
            return string.Join(Environment.NewLine, errorMessages);
        }
        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException("ValidationErrorsToStringConverter不支持反向转换");
    }
}

/// <summary>
/// 布尔值到字符串转换器
/// </summary>
/// <remarks>
/// 功能：根据布尔值和参数返回不同的字符串
/// 参数格式："TrueText|FalseText"
/// 
/// 示例：
/// - value=true, parameter="启用|禁用" → "启用"
/// - value=false, parameter="启用|禁用" → "禁用"
/// 
/// 使用场景：状态文本显示、按钮文本切换等
/// </remarks>
public class BoolToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue && parameter is string paramStr)
        {
            var parts = paramStr.Split('|');
            if (parts.Length == 2)
            {
                return boolValue ? parts[0] : parts[1];
            }
        }
        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException("BoolToStringConverter不支持反向转换");
    }
}

/// <summary>
/// Null到布尔值转换器
/// </summary>
/// <remarks>
/// 功能：检查值是否为null
/// - null → true
/// - 非null → false
/// 
/// 使用场景：控制UI元素的可见性、启用状态等
/// </remarks>
public class NullToBoolConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value == null;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException("NullToBoolConverter不支持反向转换");
    }
}

/// <summary>
/// Null到可见性转换器
/// </summary>
/// <remarks>
/// 功能：根据值是否为null控制UI元素可见性
/// - null → Collapsed
/// - 非null → Visible
/// - parameter="Inverse" 可反转逻辑
/// 
/// 使用场景：条件显示UI元素
/// </remarks>
public class NullToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        bool isNull = value == null;
        bool inverse = parameter?.ToString() == "Inverse";

        if (inverse)
            isNull = !isNull;

        return isNull ? Visibility.Collapsed : Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException("NullToVisibilityConverter不支持反向转换");
    }
}



/// <summary>
/// 布尔值转选择模式转换器
/// </summary>
public class BooleanToSelectionModeConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool allowSelection)
        {
            return allowSelection ? SelectionMode.Extended : SelectionMode.Single;
        }
        return SelectionMode.Single;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is SelectionMode selectionMode)
        {
            return selectionMode == SelectionMode.Extended;
        }
        return true;
    }
}
